import tkinter as tk
from tkinter import messagebox
import sqlite3
import os
import torch
import numpy as np

# 假设你的核心函数都已在 routeDecide_RL.py 中
from routeDecide_RL import routeDecide_RL, get_all_test_states, verify_in_db_efficient

class TestApp:
    def __init__(self, root):
        self.root = root
        self.root.title("SAC 模型测试工具")
        
        self.create_widgets()

    def create_widgets(self):
        # 创建一个主框架
        main_frame = tk.Frame(self.root, padx=10, pady=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 结果显示区域
        self.results_text = tk.Text(main_frame, height=20, width=80)
        self.results_text.pack(fill=tk.BOTH, expand=True, pady=5)
        self.results_text.config(state=tk.DISABLED) # 默认禁用，只读

        # 操作区域
        control_frame = tk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)

        # 单个测试按钮
        single_test_frame = tk.LabelFrame(control_frame, text="单个测试", padx=10, pady=10)
        single_test_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.single_button = tk.Button(single_test_frame, text="运行单个测试", command=self.run_single_test)
        self.single_button.pack(pady=5)

        # 批量测试按钮
        batch_test_frame = tk.LabelFrame(control_frame, text="批量测试", padx=10, pady=10)
        batch_test_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.batch_button = tk.Button(batch_test_frame, text="运行批量测试", command=self.run_batch_test)
        self.batch_button.pack(pady=5)

        # 手动测试区域
        manual_frame = tk.LabelFrame(main_frame, text="手动输入测试", padx=10, pady=10)
        manual_frame.pack(fill=tk.X, pady=5)
        
        self.entries = {}
        labels = ["网络大小", "邻居数量", "经度", "纬度", 
                  "平均连接率", "邻居变化率", "数据速率", "协议"]
        
        for i, label_text in enumerate(labels):
            row_frame = tk.Frame(manual_frame)
            row_frame.pack(fill=tk.X, pady=2)
            label = tk.Label(row_frame, text=label_text + ":")
            label.pack(side=tk.LEFT, padx=5)
            entry = tk.Entry(row_frame)
            entry.pack(side=tk.RIGHT, expand=True, fill=tk.X)
            self.entries[label_text] = entry
        
        self.manual_button = tk.Button(manual_frame, text="运行手动测试", command=self.run_manual_test)
        self.manual_button.pack(pady=5)

        # 填充默认值
        self.entries["网络大小"].insert(0, "150")
        self.entries["邻居数量"].insert(0, "86")
        self.entries["经度"].insert(0, "78.37")
        self.entries["纬度"].insert(0, "33.19")
        self.entries["平均连接率"].insert(0, "0.45")
        self.entries["邻居变化率"].insert(0, "0.33")
        self.entries["数据速率"].insert(0, "0.33")
        self.entries["协议"].insert(0, "AODV")
    
    def _clear_and_set_title(self, title):
        self.results_text.config(state=tk.NORMAL)
        self.results_text.delete('1.0', tk.END)
        self.results_text.insert(tk.END, f"================ {title} ================\n\n")

    def format_results(self, interference, action, result_text=""):
        """
        一次性构建完整的字符串，并返回。
        """
        output_str = (
            f"**测试状态:**\n"
            f"  网络大小: {interference[0]} 个\n"
            f"  邻居数量: {interference[1]} 个\n"
            f"  经度: {interference[2]} 度\n"
            f"  纬度: {interference[3]} 度\n"
            f"  平均连接率: {interference[4]}\n"
            f"  邻居变化率: {interference[5]}\n"
            f"  数据速率: {interference[6]}\n"
            f"  协议: {interference[7]}\n"
            f"\n"
            f"**模型决策:** \n"
            f"  AODV路由发现时间: {action[0]} s\n"
            f"  OLSR拓扑控制间隙: {action[1]} s\n"
        )
        if result_text:
            output_str += f"**验证结果:** {result_text}\n"
        
        output_str += "-----------------------------------------\n\n"
        return output_str

    def run_single_test(self):
        self._clear_and_set_title("单个测试")
        
        try:
            # 你的原始单个测试参数
            interference = [150, 86, 78.37, 33.19, 0.45, 0.33, 0.33, 'AODV']
            
            with torch.no_grad():
                action = routeDecide_RL(interference, Normalized=False)
            
            # 一次性插入格式化后的字符串
            formatted_output = self.format_results(interference, action)
            self.results_text.insert(tk.END, formatted_output)
            
        except Exception as e:
            messagebox.showerror("错误", f"单个测试中发生错误: {e}")
            self.results_text.insert(tk.END, f"发生错误: {e}\n")
        finally:
            self.results_text.config(state=tk.DISABLED)

    def run_batch_test(self):
        self._clear_and_set_title("批量测试")
        
        db_path = os.path.join('data', 'test_data.db')
        total_correct = 0
        total_evaluated = 0
        
        conn = None
        cursor = None
        
        try:
            all_states = get_all_test_states(db_path)
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 构建一个完整的报告字符串
            batch_report_str = ""
            for state in all_states:
                interference = [state[0], state[1], state[2], state[3], state[4], state[5], state[6], state[7]]
                
                with torch.no_grad():
                    action = routeDecide_RL(interference, Normalized=True)
                
                is_correct = verify_in_db_efficient(state, action, cursor)
                
                result_text = "✅ 正确" if is_correct else "❌ 错误"
                if is_correct:
                    total_correct += 1
                
                total_evaluated += 1
                
                # 拼接每个测试案例的格式化结果
                # batch_report_str += self.format_results(interference, action, result_text)
            
        except Exception as e:
            messagebox.showerror("错误", f"批量评估中发生错误: {e}")
            self.results_text.insert(tk.END, f"\n批量评估中发生错误: {e}\n")
            return
        finally:
            if cursor: cursor.close()
            if conn: conn.close()

        # 一次性插入所有批量测试结果
        self.results_text.insert(tk.END, batch_report_str)
        
        accuracy = total_correct / total_evaluated if total_evaluated > 0 else 0.0
        self.results_text.insert(tk.END, f"================================\n")
        self.results_text.insert(tk.END, f"模型在测试数据库上的正确率为: {accuracy:.2f} ({total_correct} / {total_evaluated})\n")
        self.results_text.config(state=tk.DISABLED)

    def run_manual_test(self):
        self._clear_and_set_title("手动测试")

        try:
            # 获取并转换输入数据
            network_size = float(self.entries["网络大小"].get())
            neighbor_count = float(self.entries["邻居数量"].get())
            longitude = float(self.entries["经度"].get())
            latitude = float(self.entries["纬度"].get())
            avg_connectivity = float(self.entries["平均连接率"].get())
            neighbor_change_rate = float(self.entries["邻居变化率"].get())
            data_rate = float(self.entries["数据速率"].get())
            protocol = self.entries["协议"].get()

            interference = [network_size, neighbor_count, longitude, latitude, avg_connectivity, neighbor_change_rate, data_rate, protocol]
            
            with torch.no_grad():
                action = routeDecide_RL(interference, Normalized=False)
            
            # 一次性插入格式化后的字符串
            formatted_output = self.format_results(interference, action)
            self.results_text.insert(tk.END, formatted_output)
            
        except ValueError:
            messagebox.showerror("输入错误", "请确保所有参数为有效数字。")
        except Exception as e:
            messagebox.showerror("错误", f"手动测试中发生错误: {e}")
        finally:
            self.results_text.config(state=tk.DISABLED)

if __name__ == '__main__':
    root = tk.Tk()
    app = TestApp(root)
    root.mainloop()