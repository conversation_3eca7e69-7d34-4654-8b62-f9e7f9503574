"""
App界面核心类

统筹其他四个模块，构建完整界面
"""

import tkinter as tk
from tkinter import filedialog
from ui.train_module import TrainModule
from ui.eval_module import EvalModule
from ui.test_module import TestModule
from ui.progress_bar import ProgressBarModule

class App(tk.Tk):
    """
    主应用程序类，负责创建和管理整个UI界面。
    它继承自tkinter.Tk，是GUI应用程序的根窗口。
    """
    def __init__(self):
        """
        初始化应用程序，设置窗口标题、尺寸和背景颜色。
        同时调用create_widgets方法来构建所有UI组件。
        """
        super().__init__()
        self.title("干扰识别与融合决策系统")
        
        # 设置窗口初始尺寸（不设置最小尺寸限制）
        self.window_width = 1280
        self.window_height = 800
        self.geometry(f"{self.window_width}x{self.window_height}")
        
        # 计算屏幕中心位置
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        x = (screen_width - self.window_width) // 2
        y = (screen_height - self.window_height) // 2
        self.geometry(f"+{x}+{y}")  # 设置窗口位置在屏幕中心
        
        # 设置背景色
        self.configure(bg="#f0f2f5")
        
        # 允许主窗口的大小调整
        self.resizable(True, True)
        
        # 创建一个主框架来容纳所有组件，使用grid布局
        self.main_frame = tk.Frame(self, bg="#f0f2f5")
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 配置网格布局的权重，实现按比例缩放
        # 列配置：左侧60%，右侧40%
        self.main_frame.grid_columnconfigure(0, weight=60)  # 左侧区域
        self.main_frame.grid_columnconfigure(1, weight=40)  # 右侧区域
        
        # 主框架只有一行，左右两个区域各自管理内部行布局
        self.main_frame.grid_rowconfigure(0, weight=1)

        self.create_widgets()

    def create_widgets(self):
        """
        创建并布局主界面的所有UI框架和模块。
        包括训练模块、评估模块、测试模块和进度条模块。
        """
        padding = 5  # 组件间距
        
        # === 创建左侧区域框架（训练+进度条） ===
        left_frame = tk.Frame(self.main_frame, bg="#f0f2f5")
        left_frame.grid(row=0, column=0, sticky="nsew", padx=(0, padding//2))
        
        # 左侧区域行配置：80%训练，20%进度条
        left_frame.grid_rowconfigure(0, weight=80)  # 训练区域
        left_frame.grid_rowconfigure(1, weight=20)  # 进度条区域
        left_frame.grid_columnconfigure(0, weight=1)
        
        # === 创建右侧区域框架（评估+测试） ===
        right_frame = tk.Frame(self.main_frame, bg="#f0f2f5")
        right_frame.grid(row=0, column=1, sticky="nsew", padx=(padding//2, 0))
        
        # 右侧区域行配置：50%评估，50%测试
        right_frame.grid_rowconfigure(0, weight=50)  # 评估区域
        right_frame.grid_rowconfigure(1, weight=50)  # 测试区域
        right_frame.grid_columnconfigure(0, weight=1)
        
        # --- 训练模块区域（左侧上部）---
        train_frame = tk.Frame(left_frame, bg="white", highlightbackground="#ddd", highlightthickness=1)
        train_frame.grid(row=0, column=0, sticky="nsew", padx=0, pady=(0, padding//2))
        
        # 确保训练frame内部可以自适应
        train_frame.grid_rowconfigure(0, weight=1)
        train_frame.grid_columnconfigure(0, weight=1)
        
        train_wrapper = tk.Frame(train_frame, bg="white")
        train_wrapper.grid(row=0, column=0, sticky="nsew")
        train_wrapper.grid_rowconfigure(1, weight=1)  # 内容区域可扩展
        train_wrapper.grid_columnconfigure(0, weight=1)
        
        train_title = tk.Label(train_wrapper, text="训练模块", font=("Microsoft YaHei", 16, "bold"), 
                             bg="white", fg="#2c3e50")
        train_title.grid(row=0, column=0, pady=(15, 10))
        
        train_page_container = tk.Frame(train_wrapper, bg="white")
        train_page_container.grid(row=1, column=0, sticky="nsew", padx=20, pady=(0, 20))

        # --- 进度条区域（左侧下部）---
        progress_frame = tk.Frame(left_frame, bg="white", highlightbackground="#ddd", highlightthickness=1)
        progress_frame.grid(row=1, column=0, sticky="nsew", padx=0, pady=(padding//2, 0))
        
        # 确保进度条frame内部可以自适应
        progress_frame.grid_rowconfigure(0, weight=1)
        progress_frame.grid_columnconfigure(0, weight=1)
        
        progress_wrapper = tk.Frame(progress_frame, bg="white")
        progress_wrapper.grid(row=0, column=0, sticky="nsew")
        self.progress_bar_module = ProgressBarModule(progress_wrapper)

        # --- 模型评估区域（右侧上部）---
        eval_frame = tk.Frame(right_frame, bg="white", highlightbackground="#ddd", highlightthickness=1)
        eval_frame.grid(row=0, column=0, sticky="nsew", padx=0, pady=(0, padding//2))
        
        # 确保评估frame内部可以自适应
        eval_frame.grid_rowconfigure(0, weight=1)
        eval_frame.grid_columnconfigure(0, weight=1)
        
        eval_wrapper = tk.Frame(eval_frame, bg="white")
        eval_wrapper.grid(row=0, column=0, sticky="nsew")
        eval_wrapper.grid_rowconfigure(1, weight=1)  # 内容区域可扩展
        eval_wrapper.grid_columnconfigure(0, weight=1)
        
        eval_title = tk.Label(eval_wrapper, text="模型评估", font=("Microsoft YaHei", 14, "bold"), 
                            bg="white", fg="#2c3e50")
        eval_title.grid(row=0, column=0, pady=(15, 10))
        
        eval_page_container = tk.Frame(eval_wrapper, bg="white")
        eval_page_container.grid(row=1, column=0, sticky="nsew", padx=15, pady=(0, 15))
        self.eval_module = EvalModule(eval_page_container, self.select_file)

        # --- 模型测试区域（右侧下部）---
        test_frame = tk.Frame(right_frame, bg="white", highlightbackground="#ddd", highlightthickness=1)
        test_frame.grid(row=1, column=0, sticky="nsew", padx=0, pady=(padding//2, 0))
        
        # 确保测试frame内部可以自适应
        test_frame.grid_rowconfigure(0, weight=1)
        test_frame.grid_columnconfigure(0, weight=1)
        
        test_wrapper = tk.Frame(test_frame, bg="white")
        test_wrapper.grid(row=0, column=0, sticky="nsew")
        test_wrapper.grid_rowconfigure(1, weight=1)  # 内容区域可扩展
        test_wrapper.grid_columnconfigure(0, weight=1)
        
        test_title = tk.Label(test_wrapper, text="模型测试", font=("Microsoft YaHei", 14, "bold"), 
                            bg="white", fg="#2c3e50")
        test_title.grid(row=0, column=0, pady=(15, 10))
        
        test_page_container = tk.Frame(test_wrapper, bg="white")
        test_page_container.grid(row=1, column=0, sticky="nsew", padx=15, pady=(0, 15))
        self.test_module = TestModule(test_page_container, self.select_file)

        # --- 实例化训练模块 ---
        self.train_module = TrainModule(train_page_container, self.progress_bar_module, self.select_file)

    def select_file(self, entry_widget):
        """
        打开文件选择对话框，并将选定的文件路径填充到指定的Entry控件中。
        """
        file_path = filedialog.askopenfilename()
        if file_path:
            entry_widget.delete(0, tk.END)
            entry_widget.insert(0, file_path)
