"""
甲方给了部分数据
状态空间:干扰信号调制方式
行为空间:所有可选的波形参数组合，共100种可选组合
"""

import logging
import numpy as np
import sqlite3
import os


class WaveDecideEnv:
    def __init__(self, db_path, seed = 0):
        # 状态空间和行为空间
        #self.observation_dim = [3, 8, 10]         # 定义状态空间每个维度的取值范围
        # self.action_dim = [8, 6, 3, 2]         # 定义行为空间每个维度的取值范围

        self.observation_space = {
            'feature1': {'type': 'discrete', 'n': 3},   
            'feature2': {'type': 'discrete', 'n': 8},   
            'feature3': {'type': 'discrete', 'n': 11},   # 10个离散值(1-10)， 0代表无调制方式
            # 如果有连续值，可以这样定义：
            'feature4': {
                'type': 'continuous',
                'low': 3e7,
                'high': 25e8
            },
            'feature5': {
                'type': 'continuous',
                'low': 0,
                'high': 655e6
            },
            'feature6': {
                'type': 'continuous',
                'low': -100,
                'high': 100
            }
        }
        self.observation_shape = (len(self.observation_space), )
        # self.observation_num = np.prod(self.observation_dim)

        # self.action_num = np.prod(self.action_dim)
        self.action_space = {
            'type': 'discrete',
            'n': 100,
            'dim': [100,]
        }

        # 连接数据库
        self.conn = sqlite3.connect(db_path)
        self.cursor = self.conn.cursor()


        # 获取所有唯一的状态
        self.all_train_states = self.get_all_train_states()
        self.num_train_states = len(self.all_train_states)
        self.idx_state = 0
 
        # 当前状态
        self.state = None

        # 用于跟踪环境终止后智能体继续执行的步数的变量
        self.steps_beyond_terminated = None

        # 初始化随机数生成器，使用默认种子0
        self.seed(seed)


    
    def step(self, action):
        # 动作与状态检查
        err_msg = f"{action!r} ({type(action)}) invalid"
        assert 0 <= action < self.action_space['n'], err_msg
        assert self.state is not None, "Call reset before using step method."  

        # 判断终止条件
        terminated = bool(
            self.idx_state >= self.num_train_states - 1
        )

        current_action = self.index_to_action(action)
        self.idx_state += 1

        if not terminated:
            reward = self.calculate_reward(self.state, current_action)
            self.state = self.get_state(self.idx_state)
        elif self.steps_beyond_terminated is None:
            # 智能体尝试在终止状态执行动作
            self.steps_beyond_terminated = 0
            reward = self.calculate_reward(self.state, current_action)
        else:
            if self.steps_beyond_terminated == 0:
                logging.warning(
                    "You are calling 'step()' even though this "
                    "environment has already returned terminated = True. You "
                    "should always call 'reset()' once you receive 'terminated = "
                    "True' -- any further steps are undefined behavior."
                )
            self.steps_beyond_terminated += 1
        
        return self.state, reward, terminated, {}

    def reset(self):
        # 使用独立的随机数生成器而不是全局的np.random
        # self.current_row = int(self.rng.integers(0, self.action_space['n']))
        self.idx_state = 0

        self.state = self.get_state(self.idx_state)

        self.steps_beyond_terminated = None

        # print(f"当前状态: state={self.state}")
        return self.state

    def get_state(self, row_index):
        """获取指定行的状态参数"""
        if 0 <= row_index < self.num_train_states:
            state = self.all_train_states[row_index]
            return state
        return None


    def index_to_action(self, action_index):
        """将一维动作索引转换为数据库中的动作值"""
        if not (0 <= action_index < self.action_space['n']):
            raise ValueError(f"动作索引 {action_index} 超出范围 [0-{self.action_space['n']-1}]")
        
        # 将动作索引转换为数据库中的动作值 (1-100)
        action_value = action_index + 1
        
        return np.array([action_value], dtype=np.float64)

    def calculate_reward(self, state, action):

        query = '''
        SELECT performance
        FROM train_table
        WHERE "feature1"=? AND "feature2"=? AND "feature3"=? AND "feature4"=? AND "feature5"=? AND "feature6"=?
        AND "action"=?
        '''
        # 处理动作格式：如果是数组，取第一个元素
        if isinstance(action, (list, np.ndarray)):
            action_value = action[0] if len(action) > 0 else action
        else:
            action_value = action
            
        params = tuple(state) + (action_value,)
        
        self.cursor.execute(query, params)
        result = self.cursor.fetchone()
        
        if result is not None:
            sir = result[0]
            # print(f"找到匹配的记录: state={state}, action={action}")
            return (30 - sir) / 30.0
        else:
            print(f"未找到匹配的记录: state={state}, action={action}")
            return -0.1

    def action_sample(self):
        # 使用独立的随机数生成器
        return int(self.rng.integers(0, self.action_space['n']))



    def close(self):
        """关闭环境，释放资源"""
        if hasattr(self, 'cursor') and self.cursor is not None:
            self.cursor.close()
        if hasattr(self, 'conn') and self.conn is not None:
            self.conn.close()

    def seed(self, seed):
         # 创建独立的随机数生成器
        seed_seq = np.random.SeedSequence(seed)
        self.rng = np.random.Generator(np.random.PCG64(seed_seq))
        
        return [seed]

    def get_all_train_states(self):
        """
        从训练数据库中获取所有唯一的状态

        Returns:
            list: 所有唯一的状态列表
        """
        # 获取所有唯一的状态组合（使用已经建立的数据库连接）
        query = '''
        SELECT DISTINCT "feature1", "feature2", "feature3", "feature4", "feature5", "feature6"
        FROM train_table
        ORDER BY "feature1", "feature2", "feature3", "feature4", "feature5", "feature6"
        '''
        
        self.cursor.execute(query)
        states = self.cursor.fetchall()
        
        # 转换为numpy数组
        states_array = np.array(states, dtype=np.float64)
        
        # print(f"📊 从测试数据库中找到 {len(states_array)} 个唯一状态")
        return states_array
