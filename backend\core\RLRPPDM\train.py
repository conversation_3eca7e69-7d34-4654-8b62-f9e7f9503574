import os
import yaml # 用于处理 YAML（YAML Ain't Markup Language）格式数据
import argparse # 用于解析命令行参数
from datetime import datetime

import sys
from pathlib import Path

# 添加 RLRPPDM 目录到 Python 路径
rlrppdm_path = Path(__file__).resolve().parent
if str(rlrppdm_path) not in sys.path:
    sys.path.append(str(rlrppdm_path))

# 现在导入本地的 sacd 模块
from sacd.env import RouteDecideEnv
from sacd.agent import SacdAgent


def run(args):
    with open(args.config, encoding = 'utf-8') as f:
        config = yaml.load(f, Loader=yaml.SafeLoader)

    # Create environments.
    env = RouteDecideEnv() 
    test_env = RouteDecideEnv()

    # Specify the directory to log.
    name = os.path.splitext(os.path.basename(args.config))[0]
    time = datetime.now().strftime("%Y%m%d-%H%M%S")
    log_dir = os.path.join(
        'logs', f'{name}-seed{args.seed}-{time}')

    # Create the agent.
    agent = SacdAgent(
        env=env, test_env=test_env, log_dir=log_dir, cuda=args.cuda,
        seed=args.seed, **config)
    agent.run()

def model_train_RLRPPDM(training_params, progress_callback):
    """
    强化学习路由参数决策模型的训练函数
    
    Args:
        training_params (dict): 训练参数
        progress_callback (callable): 进度回调函数。返回False表示应该停止训练。
            函数格式：callback(progress, message, model_path=None) -> bool
    
    Returns:
        str: 训练完成后的模型保存路径，如果训练被终止返回None
    """
    try:
        config_path = os.path.join(os.path.dirname(__file__), 'config', 'sacd.yaml')
        
        # 初始化进度
        if not progress_callback(0, "正在读取配置文件..."):
            return None
            
        # 读取基础配置
        with open(config_path, encoding='utf-8') as f:
            config = yaml.load(f, Loader=yaml.SafeLoader)
        
        # 添加训练所需的基础配置参数
        if 'cuda' not in config:
            config['cuda'] = True  # 默认使用 CUDA
        if 'seed' not in config:
            config['seed'] = training_params.get('random_seed', 0)
        if 'optimizer' not in config:
            config['optimizer'] = training_params.get('optimizer', 'adam')
            
        # 如果training_params中提供了这些参数，就使用它们覆盖配置
        if 'epochs' in training_params:
            config['num_steps'] = training_params['epochs'] * 1000  # 假设每个epoch大约1000步
        if 'batch_size' in training_params:
            config['batch_size'] = training_params['batch_size']
        if 'learning_rate' in training_params:
            config['lr'] = training_params['learning_rate']
                
        # 初始化进度
        if not progress_callback(5, "正在初始化训练环境..."):
            return None
            
        # 创建环境
        env = RouteDecideEnv(training_params.get('train_data_path'))
        test_env = RouteDecideEnv(training_params.get('train_data_path'))
        
        # 指定日志目录
        name = os.path.splitext(os.path.basename(config_path))[0]
        time = datetime.now().strftime("%Y%m%d-%H%M%S")
        log_dir = os.path.join(os.path.dirname(__file__), 'logs', f'{name}-seed{config["seed"]}-{time}')
        
        if not progress_callback(10, "正在创建强化学习代理..."):
            return None
            
        # 创建代理
        agent = SacdAgent(
            env=env, 
            test_env=test_env, 
            log_dir=log_dir, 
            **config  # config 中已经包含了所有需要的参数，包括 cuda 和 seed，以及 optimizer
        )
        
        # 定义训练回调函数来更新进度
        def training_progress(step, total_steps, info):
            progress = 10 + int(90 * step / total_steps)  # 10-100%的进度范围
            message = f"训练进度：{step}/{total_steps} 步 - {info}"
            return progress_callback(progress, message)
            
        # 设置代理的进度回调
        agent.set_progress_callback(training_progress)
        
        # 开始训练
        if not progress_callback(10, "开始训练..."):
            return None
            
        # 运行训练
        print("开始执行agent.run()...")
        model_path = agent.run()
        print(f"agent.run()执行完成，返回model_path: {model_path}")
        
        # 训练完成
        if model_path:
            print("训练成功完成，准备调用最终进度回调")
            if progress_callback(100, "训练完成！最优模型已保存", model_path):
                return model_path
        else:
            print("训练未能产生有效的模型路径")
            # 添加未产生最优模型的提示
            progress_callback(100, "训练完成，但未能产生性能超过初始状态的模型")
        return None
        
    except Exception as e:
        progress_callback(-1, f"训练过程中发生错误: {str(e)}")
        return None


# __name__ 是一个特殊变量，当模块被直接运行时，其值为 '__main__'
if __name__ == '__main__':
    parser = argparse.ArgumentParser()  # 通过创建这个对象，你可以定义程序可以接受的命令行参数，并解析这些参数
    parser.add_argument('--config', type=str, default=os.path.join('config', 'sacd.yaml'))  # 用于指定配置文件的路径
    parser.add_argument('--cuda', action='store_true')  # 一个布尔类型的参数，用于指示是否使用CUDA加速
    parser.add_argument('--seed', type=int, default=0)  # 用于指定随机种子，默认值为 0 。这个参数用于确保实验的可重复性。
    args = parser.parse_args()  # 读取命令行输入的参数，并将其解析为一个命名空间对象 args
    run(args)
