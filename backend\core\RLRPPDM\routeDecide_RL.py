import torch
import numpy as np
import sqlite3
import os
import sys
from pathlib import Path

# 添加 RLRPPDM 目录到 Python 路径
rlrppdm_path = Path(__file__).resolve().parent
if str(rlrppdm_path) not in sys.path:
    sys.path.append(str(rlrppdm_path))

from .sacd.model import CateoricalPolicy

def load_policy_model_auto(model_path):
    """
    自动从保存的模型中获取网络结构参数并加载策略模型
    """
    # 路径判断
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件未找到: {model_path}")

    # 加载模型权重
    state_dict = torch.load(model_path, map_location='cuda')
    
    # 自动查找第一层和最后一层
    first_layer_weight = None
    last_layer_weight = None
    
    # 收集所有线性层的权重
    linear_weights = {}
    for key, value in state_dict.items():
        if 'weight' in key and 'head' in key:
            # 提取层索引
            layer_idx = int(key.split('.')[1])
            linear_weights[layer_idx] = value
            
    if not linear_weights:
        raise ValueError("未找到线性层权重")
    
    # 获取最后一层（索引最大的层）
    first_layer_idx = min(linear_weights.keys())
    first_layer_weight = linear_weights[first_layer_idx]
    last_layer_idx = max(linear_weights.keys())
    last_layer_weight = linear_weights[last_layer_idx]
    
    # print(f"🔍 自动检测的网络结构:")
    # print(f"   第一层: head.{first_layer_idx}.weight -> {first_layer_weight.shape}")
    # print(f"   最后一层: head.{last_layer_idx}.weight -> {last_layer_weight.shape}")
    # print(f"   总层数: {len(linear_weights)} 个线性层")
    
    # 推断参数
    state_dim = first_layer_weight.shape[1]  # 输入维度
    num_actions = last_layer_weight.shape[0]  # 输出维度
    
    # print(f"   推断参数: state_dim={state_dim}, num_actions={num_actions}")
    
    # 创建策略网络（使用默认参数）
    policy = CateoricalPolicy(state_dim=state_dim, num_actions=num_actions)
    
    # 加载权重
    policy.load_state_dict(state_dict)
    policy.eval()
    
    return policy

def get_all_test_states(db_path='data/test_data.db', table_name='test_table'):
    """
    从测试数据库中获取所有唯一的状态
    
    Args:
        db_path: 测试数据库路径
        table_name: 表名
    
    Returns:
        list: 所有唯一的状态列表
    """
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 获取所有唯一的状态组合
    query = f'''
    SELECT DISTINCT "feature1", "feature2", "feature3", "feature4", "feature5", "feature6", "feature7", "feature8"
    FROM {table_name}
    ORDER BY "feature1", "feature2", "feature3", "feature4", "feature5", "feature6", "feature7", "feature8"
    '''
    
    cursor.execute(query)
    states = cursor.fetchall()
    
    # 转换为numpy数组
    states_array = np.array(states, dtype=np.float64)
    
    conn.close()
    
    # print(f"📊 从测试数据库中找到 {len(states_array)} 个唯一状态")
    return states_array

def routeDecide_RL(interference, model_path,Normalized = False):
    policy = load_policy_model_auto(model_path)

    network_size = interference[0]
    neighbor_count = interference[1]
    longitude = interference[2]
    latitude = interference[3]
    avg_connectivity = interference[4]
    neighbor_change_rate = interference[5]
    data_rate = interference[6]
    protocol = interference[7]

    if isinstance(protocol, str):
        FEATURE8_MAPPING = {
        'AODV': 0,
        'OLSR': 1,
        }
        protocol = FEATURE8_MAPPING.get(protocol)

    if not Normalized:
        network_size = np.round(np.float64((256 - network_size) / 256), 6)
        neighbor_count = np.round(np.float64((256 - neighbor_count) / 256), 6)
        longitude = np.round(np.float64((longitude - (-180)) / (180 - (-180))), 6)
        latitude = np.round(np.float64((latitude - (-90)) / (90 - (-90))), 6)            
        avg_connectivity = np.round(np.float64((5 - avg_connectivity) / 5), 6)
        data_rate = np.round(np.float64((10 - data_rate) / 10), 6)


    state = np.array([network_size, neighbor_count, longitude, latitude, avg_connectivity, neighbor_change_rate, data_rate, protocol])
    state = torch.tensor(state, dtype=torch.float32).unsqueeze(0)
    with torch.no_grad():
        action_index = policy.act(state)
        action = index_to_action(action_index.item())
    return action

def index_to_action(action_index):
    """将一维动作索引转换为数据库中的动作值"""
    # 根据你的互斥二维动作映射逻辑
    if 0 <= action_index <= 9:
        dim1_value = (action_index + 1) * 0.1
        action_tuple = (round(dim1_value, 1), 0)
    else: # 10 <= action_index <= 19
        dim2_value = action_index - 9
        action_tuple = (0, dim2_value)

    # 关键步骤：将元组转换为指定格式的 NumPy 数组
    return np.array(action_tuple, dtype=np.float64)

import sqlite3
import numpy as np

def verify_in_db_efficient(state, action, cursor, table_name='test_table'):
    """
    在测试数据库中验证给定的状态-动作组合是否存在。

    Args:
        state (list/np.ndarray): 包含8个特征的状态。
        action (list/np.ndarray): 包含2个动作维度的动作。
        cursor (sqlite3.Cursor): 已经连接到数据库的游标。

    Returns:
        bool: 如果组合存在则返回 True，否则返回 False。
    """
    try:
        query = f'''
        SELECT EXISTS(
            SELECT 1
            FROM {table_name}
            WHERE "feature1"=? AND "feature2"=? AND "feature3"=? AND "feature4"=? 
              AND "feature5"=? AND "feature6"=? AND "feature7"=? AND "feature8"=?
              AND "action1"=? AND "action2"=?
        )
        '''
        
        params = tuple(state) + tuple(action)
        
        cursor.execute(query, params)
        result = cursor.fetchone()
        
        return result is not None and result[0] == 1

    except Exception as e:
        print(f"验证过程中发生意外错误: {e}")
        return False

def model_evaluate_RLRPPDM(model_path, data_path, table_name='test_table'):
    all_states = get_all_test_states(data_path)

    total_correct = 0
    total_evaluated = 0

    conn = None
    cursor = None
    try:
        # 在循环开始前，建立一次数据库连接
        conn = sqlite3.connect(data_path)
        cursor = conn.cursor()

        for state in all_states:
            # 1. 在循环内部更新 interference 变量
            interference = [state[0], state[1], state[2], state[3], state[4], state[5], state[6], state[7]]

            # 2. 让模型生成动作
            with torch.no_grad():
                action = routeDecide_RL(interference, model_path, Normalized = True)
            
            # 3. 使用新的高效函数进行验证
            is_correct = verify_in_db_efficient(state, action, cursor, table_name)
            
            if is_correct:
                total_correct += 1
            
            total_evaluated += 1
            # print(f"""network_size: {network_size}, neighbor_count: {neighbor_count}, longitude: {longitude}, latitude: {latitude},
            #     avg_connectivity: {avg_connectivity}, neighbor_change_rate: {neighbor_change_rate}, data_rate: {data_rate}, protocol: {protocol}""")
            # print(f"action: {action}")

        # 计算正确率
        accuracy = total_correct / total_evaluated
        print(f"模型在测试数据库上的正确率为: {accuracy:.2f}")
        # print(f"total_correct: {total_correct}, total_evaluated: {total_evaluated}")
    except Exception as e:
        print(f"评估循环中发生错误: {e}")

    finally:
        # 在所有工作完成后，统一关闭连接
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def model_test_RLRPPDM(model_path, params):
    """
    使用强化学习模型进行路由参数决策测试

    Args:
        model_path (str): 模型文件路径
        params (dict): 测试参数字典，包含干扰特征

    Returns:
        str: 测试结果描述
    """
    try:
        # 从参数中提取干扰特征
        interference = [
            float(params.get('网络规模', 0)),
            float(params.get('邻居个数', 0)),
            float(params.get('经度', 0)),
            float(params.get('维度', 0)),
            float(params.get('平均连接度', 0)),
            float(params.get('邻居变化率', 0)),
            float(params.get('数据速率', 0)),
            params.get('路由协议', 'AODV')
        ]

        print(f"测试参数 - system: {interference[0]}, style: {interference[1]}, module: {interference[2]}")
        print(f"freq: {interference[3]}, bandwidth: {interference[4]}, power: {interference[5]}")

        # 调用波形决策函数
        with torch.no_grad():
            action = waveDecide_RL(interference, model_path, Normalized=False)

        # 格式化结果
        result = f"测试完成！\n"
        result += f"输入特征: 体制={interference[0]}, 样式={interference[1]}, 调制方式={interference[2]}\n"
        result += f"中心频率={interference[3]}, 带宽={interference[4]}, 功率={interference[5]}\n"
        result += f"决策结果: {action}"

        return result

    except Exception as e:
        return f"测试失败: {str(e)}"    




if __name__ == "__main__":
    # ================ 测试单个状态 ================
    print("=============== 测试单个状态 ================\n")
    network_size = 150
    neighbor_count = 86
    longitude = 78.37
    latitude = 33.19
    avg_connectivity = 0.33
    neighbor_change_rate = 0.45
    data_rate = 0.33
    protocol = 'AODV'
    interference = [network_size, neighbor_count, longitude, latitude, avg_connectivity, neighbor_change_rate, data_rate, protocol]
    action = routeDecide_RL(interference, Normalized = False)
    print(f"""network_size: {network_size}, neighbor_count: {neighbor_count}, longitude: {longitude}, latitude: {latitude},
        avg_connectivity: {avg_connectivity}, neighbor_change_rate: {neighbor_change_rate}, data_rate: {data_rate}, protocol: {protocol}""")
    print(f"action: {action}\n")

    # ================ 测试所有状态 ================
    print("=============== 测试集所有状态 ================\n")
    db_path = os.path.join('data', 'test_data.db')
    all_states = get_all_test_states(db_path)

    total_correct = 0
    total_evaluated = 0

    conn = None
    cursor = None
    try:
        # 在循环开始前，建立一次数据库连接
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        for state in all_states:
            # 1. 在循环内部更新 interference 变量
            interference = [state[0], state[1], state[2], state[3], state[4], state[5], state[6], state[7]]

            # 2. 让模型生成动作
            with torch.no_grad():
                action = routeDecide_RL(interference, Normalized = True)
            
            # 3. 使用新的高效函数进行验证
            is_correct = verify_in_db_efficient(state, action, cursor)
            
            if is_correct:
                total_correct += 1
            
            total_evaluated += 1
            # print(f"""network_size: {network_size}, neighbor_count: {neighbor_count}, longitude: {longitude}, latitude: {latitude},
            #     avg_connectivity: {avg_connectivity}, neighbor_change_rate: {neighbor_change_rate}, data_rate: {data_rate}, protocol: {protocol}""")
            # print(f"action: {action}")

        # 计算正确率
        accuracy = total_correct / total_evaluated
        print(f"模型在测试数据库上的正确率为: {accuracy:.2f}")
        # print(f"total_correct: {total_correct}, total_evaluated: {total_evaluated}")
    except Exception as e:
        print(f"评估循环中发生错误: {e}")

    finally:
        # 在所有工作完成后，统一关闭连接
        if cursor:
            cursor.close()
        if conn:
            conn.close()


    # ================ 测试输入状态 ================
    print("=============== 测试输入状态 ================\n")
    while True:
        user_input = input("请输入八个参数(network_size, neighbor_count, longitude, latitude, avg_connectivity, neighbor_change_rate, data_rate, protocol),用空格分隔,或输入q退出: ")
        if user_input.strip().lower() == 'q':
            print("已退出。")
            break
        try:
            # 使用 split() 方法将字符串拆分成一个列表
            input_parts = user_input.strip().split()

            # 提取并转换前8个数字
            network_size = float(input_parts[0])
            neighbor_count = float(input_parts[1])
            longitude = float(input_parts[2])
            latitude = float(input_parts[3])
            avg_connectivity = float(input_parts[4])
            neighbor_change_rate = float(input_parts[5])
            data_rate = float(input_parts[6])

            # 提取字符串（protocol）
            protocol = input_parts[7]
        except ValueError:
            print("输入格式有误，请重新输入。")
            continue

        interference = [network_size, neighbor_count, longitude, latitude, avg_connectivity, neighbor_change_rate, data_rate, protocol]
        action = routeDecide_RL(interference, Normalized = False)
        print(f"动作: {action}\n")
