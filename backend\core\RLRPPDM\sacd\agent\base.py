# type: ignore
"""
abc 是 Python 的抽象基类模块，用于定义抽象类和抽象方法
ABC (Abstract Base Class):
    - 用作抽象基类的基类
    - 继承 ABC 的类可以包含抽象方法
    - 抽象类不能被直接实例化，必须通过子类实现其抽象方法
abstractmethod :
    - 装饰器decorator用于声明抽象方法
    - 被装饰的方法必须在子类中实现
    - 如果子类没有实现所有抽象方法，则不能实例化
"""
from abc import ABC, abstractmethod
import os
import numpy as np
from collections import deque
import torch
from torch.utils.tensorboard.writer import SummaryWriter   # 用于记录和可视化深度学习训练过程中的各种数据

from sacd.memory import LazyMultiStepMemory

class RunningMeanStats:

    def __init__(self, n=10):
        self.n = n
        self.stats = deque(maxlen=n)

    def append(self, x):
        self.stats.append(x)

    def get(self):
        return np.mean(list(self.stats))


class BaseAgent(ABC):

    def __init__(self, env, test_env, log_dir, num_steps=100000, batch_size=64,
                 memory_size=1000000, gamma=0.99, multi_step=1,
                 target_entropy_ratio=0.98, start_steps=20000,
                 update_interval=4, target_update_interval=8000,
                 num_eval_steps=125000, max_episode_steps=27000,
                 log_interval=10, eval_interval=1000, cuda=True, seed=0, optimizer='Adam'):
        super().__init__()  # 调用父类的初始化方法
        self.env = env
        self.test_env = test_env

        # Set seed.
        torch.manual_seed(seed) # 确保 PyTorch 的所有随机操作（如权重初始化、dropout 等）可重复
        np.random.seed(seed)    # 确保 NumPy 的随机操作可重复
        self.env.seed(seed) # 确保训练环境的随机性（如初始状态、随机动作等）可重复
        self.test_env.seed(2**31-1-seed)    # 2**31-1 是一个质数（梅森素数），可以确保测试环境的种子与训练环境的种子有较大的差异

        self.device = torch.device(
            "cuda" if cuda and torch.cuda.is_available() else "cpu")

        # LazyMemory efficiently stores FrameStacked states.
        self.memory = LazyMultiStepMemory(
            capacity=memory_size,
            state_shape=self.env.observation_shape,
            device=self.device, gamma=gamma, multi_step=multi_step)

        self.log_dir = log_dir
        self.model_dir = os.path.join(log_dir, 'model')
        self.summary_dir = os.path.join(log_dir, 'summary')
        if not os.path.exists(self.model_dir):
            os.makedirs(self.model_dir)
        if not os.path.exists(self.summary_dir):
            os.makedirs(self.summary_dir)

        self.writer = SummaryWriter(log_dir=self.summary_dir)   # 初始化 TensorBoard 日志记录器
        self.train_return = RunningMeanStats(log_interval)  # 初始化训练回报的滑动平均统计

        self.steps = 0                                      # 当前训练的总步数
        self.learning_steps = 0                             # 实际学习更新的步数
        self.episodes = 0                                   # 已训练的回合数
        self.best_eval_score = -np.inf                      # 最佳评估分数，初始化为负无穷
        self.num_steps = num_steps                          # 训练的总步数限制
        self.batch_size = batch_size                        # 每次学习使用的批量大小
        self.gamma_n = gamma ** multi_step                  # n步奖励的折扣因子
        self.start_steps = start_steps                      # 开始学习前的随机探索步数
        self.update_interval = update_interval              # 更新网络参数的间隔步数
        self.target_update_interval = target_update_interval # 更新目标网络的间隔步数
        self.num_eval_steps = num_eval_steps                # 评估阶段的最大步数
        self.max_episode_steps = max_episode_steps          # 每个回合的最大步数
        self.log_interval = log_interval                    # 日志记录的间隔回合数
        self.eval_interval = eval_interval                  # 评估的间隔步数

        self._stop_requested = False                        # 训练停止标志
        self._progress_callback = None                      # 进度回调函数

    def run(self):
        while True:
            # 更新进度并检查是否需要停止训练
            if hasattr(self, '_progress_callback') and self._progress_callback is not None:
                if not self._progress_callback(self.steps, self.num_steps, f""):
                    self._stop_requested = True
                    return None

            self.train_episode()
            
            # 检查是否达到总步数或训练被终止
            if self.steps >= self.num_steps:
                model_path = os.path.join(self.model_dir, 'final')
                self.save_models(model_path)  # 保存最后的模型

                # 返回最优模型的路径（如果存在的话）
                best_model_path = os.path.join(self.model_dir, 'best','policy.pth')
                if os.path.exists(best_model_path):
                    return best_model_path  # 返回最优模型的路径
                return None

    # is_update 方法用于判断是否进行网络更新
    # 条件1：当前步数能被更新间隔整除
    # 条件2：当前步数大于等于开始学习的步数
    def is_update(self):
        return self.steps % self.update_interval == 0\
            and self.steps >= self.start_steps

    @abstractmethod
    def explore(self, state):
        pass

    @abstractmethod
    def exploit(self, state):
        pass

    @abstractmethod
    def update_target(self):
        pass

    @abstractmethod
    def calc_current_q(self, states, actions, rewards, next_states, dones):
        pass

    @abstractmethod
    def calc_target_q(self, states, actions, rewards, next_states, dones):
        pass

    @abstractmethod
    def calc_critic_loss(self, batch):
        """计算评论家网络损失"""
        pass

    @abstractmethod
    def calc_policy_loss(self, batch):
        """计算策略网络损失"""
        pass

    @abstractmethod
    def calc_entropy_loss(self, entropies):
        """计算熵损失"""
        pass

    def train_episode(self):
        self.policy.train() # 切换策略网络到训练模式
        self.online_critic.train() # 切换在线评论家网络到训练模式

        self.episodes += 1
        episode_return = 0.
        episode_steps = 0

        done = False
        state = self.env.reset()

        while (not done) and episode_steps <= self.max_episode_steps:

            # 在初始阶段（start_steps步之前）使用随机动作进行探索
            if self.start_steps > self.steps:
                action = self.env.action_sample()  # 从动作空间中随机采样
                # print(action)
            else:
                action = self.explore(state)  # 使用策略网络进行探索

            next_state, reward, done, _ = self.env.step(action)

            # To calculate efficiently, set priority=max_priority here.
            self.memory.append(state, action, reward, next_state, done)

            self.steps += 1
            episode_steps += 1
            episode_return += reward
            state = next_state

            # 是否需要进行网络参数更新
            if self.is_update():
                self.learn()

            # 更新目标网络
            if self.steps % self.target_update_interval == 0:
                self.update_target()

            if self.steps % self.eval_interval == 0:
                self.evaluate()  # 评估当前模型性能
                

        # We log running mean of training rewards.
        self.train_return.append(episode_return)

        # 用于记录训练过程中的奖励信息到 TensorBoard
        if self.episodes % self.log_interval == 0:
            self.writer.add_scalar(
                'reward/train', self.train_return.get(), self.steps)

        print(f'Episode: {self.episodes:<4}  '
              f'Episode steps: {episode_steps:<4}  '
              f'Return: {episode_return:<5.1f}')

    def learn(self):    # hasattr() 是 Python 的一个内置函数，用于检查一个对象是否具有指定的属性。
        assert hasattr(self, 'q1_optim') and hasattr(self, 'q2_optim') and\
            hasattr(self, 'policy_optim') and hasattr(self, 'alpha_optim')

        self.learning_steps += 1

        # 返回样本批次
        batch = self.memory.sample(self.batch_size)

        # - q1_loss , q2_loss : 两个Q网络的损失值
        # - errors : TD误差，用于优先经验回放的优先级更新
        # - mean_q1 , mean_q2 : 两个Q网络的平均预测值
        q1_loss, q2_loss, mean_q1, mean_q2 = self.calc_critic_loss(batch)
        # - policy_loss : 策略网络的损失值
        # - entropies : 策略的熵值，用于衡量策略的随机性
        policy_loss, entropies = self.calc_policy_loss(batch)
        # 计算熵值损失，用于自动调节温度参数α，通过这个损失来平衡探索和利用
        # - entropy_loss : 熵值损失
        entropy_loss = self.calc_entropy_loss(entropies)

        self.update_params(self.q1_optim, q1_loss)
        self.update_params(self.q2_optim, q2_loss)
        self.update_params(self.policy_optim, policy_loss)
        self.update_params(self.alpha_optim, entropy_loss)

        self.alpha = self.log_alpha.exp()
        

        if self.learning_steps % self.log_interval == 0:
            # 记录两个 Q 网络的损失
            self.writer.add_scalar('loss/Q1', q1_loss.detach().item(), self.learning_steps)
            self.writer.add_scalar('loss/Q2', q2_loss.detach().item(), self.learning_steps)
            
            # 记录策略网络和温度参数的损失
            self.writer.add_scalar('loss/policy', policy_loss.detach().item(), self.learning_steps)
            self.writer.add_scalar('loss/alpha', entropy_loss.detach().item(), self.learning_steps)
            
            # 记录当前温度参数值
            self.writer.add_scalar('stats/alpha', self.alpha.detach().item(), self.learning_steps)
            
            # 记录两个 Q 网络的平均预测值
            self.writer.add_scalar('stats/mean_Q1', mean_q1, self.learning_steps)
            self.writer.add_scalar('stats/mean_Q2', mean_q2, self.learning_steps)
            
            # 记录策略网络的平均熵值
            self.writer.add_scalar('stats/entropy', entropies.detach().mean().item(), self.learning_steps)

    def evaluate(self):
        self.policy.eval()  # 切换策略网络到评估模式
        self.online_critic.eval()  # 切换在线评论家网络到评估

        num_episodes = 0
        num_steps = 0
        total_return = 0.0

        while True:
            # 重置测试环境，开始新的评估回合
            state = self.test_env.reset()
            episode_steps = 0
            episode_return = 0.0
            done = False
            
            # 在单个回合中运行当前策略
            while (not done) and episode_steps <= self.max_episode_steps:
                action = self.exploit(state)  # 使用确定性策略选择动作
                next_state, reward, done, _ = self.test_env.step(action)
                num_steps += 1
                episode_steps += 1
                episode_return += reward
                state = next_state

            num_episodes += 1
            total_return += episode_return

            # 当评估步数超过限制时停止
            if num_steps > self.num_eval_steps:
                break

        # 计算平均回报
        mean_return = total_return / num_episodes

        # 如果性能超过历史最佳，保存为最佳模型
        if mean_return > self.best_eval_score:
            self.best_eval_score = mean_return
            self.save_models(os.path.join(self.model_dir, 'best'))

        # 记录评估结果
        self.writer.add_scalar('reward/test', mean_return, self.steps)
        print('-' * 60)
        print(f'Num steps: {self.steps:<5}  '
              f'return: {mean_return:<5.1f}')
        print('-' * 60)
        
        # 评估结束后，将模型切换回训练模式
        self.policy.train()
        self.online_critic.train()

    @abstractmethod
    def save_models(self, save_dir):
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

    # 这是一个析构函数( __del__ )，当对象被销毁时会自动调用。它的主要作用是进行资源清理
    def __del__(self):
        self.env.close()
        self.test_env.close()
        self.writer.close()

    def update_params(self, optim, loss, retain_graph=False):
        # - 清空所有参数的梯度
        # - 防止梯度累积
        # - 确保每次更新只使用当前批次的梯度
        optim.zero_grad()
        # - 计算损失函数关于网络参数的梯度
        # - retain_graph 参数决定是否保留计算图
        # - 如果需要多次反向传播，就需要设置 retain_graph=True
        loss.backward(retain_graph=retain_graph)
        # - 根据计算出的梯度更新网络参数
        # - 使用优化器（如Adam）定义的更新规则
        # - 朝着减小损失的方向调整参数
        optim.step()


    def disable_gradients(self, network):
        # Disable calculations of gradients.
        for param in network.parameters():  # 遍历网络中的所有参数
            param.requires_grad = False     # 设置参数不需要计算梯度