# type: ignore
"""
评估模块

选择测试集评估模型的性能
"""

import tkinter as tk
from tkinter import ttk, filedialog
from backend.manager import EvaluationManager # 导入后端评估管理类

class EvalModule:
    """
    模型评估模块的UI逻辑类。
    """
    def __init__(self, parent_frame, select_file_callback):
        """
        初始化评估模块。
        
        Args:
            parent_frame (tk.Frame): 承载该模块页面的父框架。
            select_file_callback (function): 从主应用传入的文件选择回调函数。
        """
        self.parent_frame = parent_frame
        self.select_file_callback = select_file_callback
        self.evaluation_manager = EvaluationManager() # 实例化后端管理类
        self.current_page = 0
        self.pages = []
        self.scroll_canvases = []  # 存储所有页面的画布和更新函数
        self.progress_dots = []  # 存储页面指示器圆点

        # 存储UI控件的引用
        self.eval_model_combo = None
        self.eval_model_path_entry = None
        self.eval_data_path_entry = None
        self.eval_result_label = None
        
        self.create_pages()
        self.create_nav_buttons()
        self.show_page(self.current_page)
    
    def create_scrollable_frame(self, parent):
        """
        创建一个带滚动条的框架。
        
        Args:
            parent: 父容器
            
        Returns:
            tuple: content_frame 内容框架
        """
        # 创建画布和滚动条
        canvas = tk.Canvas(parent, bg="white", highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        
        # 创建内容框架
        content_frame = tk.Frame(canvas, bg="white")
        
        # 配置画布的滚动区域
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 放置画布和滚动条
        scrollbar.pack(side="right", fill="y", padx=(0, 2))
        canvas.pack(side="left", fill="both", expand=True, padx=(10, 2))
        
        # 创建画布窗口
        window = canvas.create_window((0, 0), window=content_frame, anchor="nw")
        
        # 配置画布滚动范围
        def configure_scroll_region(event=None):
            canvas.configure(scrollregion=canvas.bbox("all"))
            # 设置内容框架的宽度，确保至少有400像素宽
            min_width = max(400, canvas.winfo_width())
            canvas.itemconfig(window, width=min_width)
        
        content_frame.bind("<Configure>", configure_scroll_region)
        canvas.bind("<Configure>", configure_scroll_region)
        
        # 绑定鼠标滚轮事件
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        canvas.bind_all("<MouseWheel>", on_mousewheel)
        self.scroll_canvases.append((canvas, configure_scroll_region))
        
        return content_frame

    def create_pages(self):
        """
        创建评估模块的四个分页。
        """
        # 设置按钮样式
        style = ttk.Style()
        style.configure('Custom.TButton', 
                      font=('Microsoft YaHei', 10),
                      padding=5)
        
        style.configure('Nav.TButton', 
                      font=('Microsoft YaHei', 10),
                      padding=5,
                      width=10)
        
        models = [
            "基于深度神经网络的波形智能决策模型",
            "基于强化学习的波形决策模型", 
            "基于决策树的MAC协议优选模型",
            "基于模糊推理神经网络的路由协议优选模型",
            "基于贝叶斯网络的路由协议参数决策模型",
            "基于强化学习的路由参数决策模型"]

        # 第一页：模型选择
        eval_page1 = tk.Frame(self.parent_frame, bg="white")
        self.pages.append(eval_page1)  # 先添加到页面列表
        
        content1 = self.create_scrollable_frame(eval_page1)
        
        title1 = tk.Label(content1, text="选择评估模型", 
                         font=("Microsoft YaHei", 14, "bold"),
                         bg="white", fg="#2c3e50")
        title1.pack(pady=(20, 15))
        
        model_frame = tk.Frame(content1, bg="white")
        model_frame.pack(fill="x")
        
        tk.Label(model_frame, text="模型类型：",
                font=("Microsoft YaHei", 10),
                bg="white", fg="#34495e").pack(anchor="w", pady=(0, 5))
                
        self.eval_model_combo = ttk.Combobox(model_frame, values=models,
                                            font=("Microsoft YaHei", 10),
                                            state="readonly", width=35)
        self.eval_model_combo.set(models[0])
        self.eval_model_combo.pack(fill="x")

        # 第二页：模型文件选择
        eval_page2 = tk.Frame(self.parent_frame, bg="white")
        self.pages.append(eval_page2)  # 先添加到页面列表
        
        content2 = self.create_scrollable_frame(eval_page2)
        
        title2 = tk.Label(content2, text="选择模型文件",
                         font=("Microsoft YaHei", 14, "bold"),
                         bg="white", fg="#2c3e50")
        title2.pack(pady=(20, 15))
        
        model_path_frame = tk.Frame(content2, bg="white")
        model_path_frame.pack(fill="x")
        
        tk.Label(model_path_frame, text="模型文件路径：",
                font=("Microsoft YaHei", 10),
                bg="white", fg="#34495e").pack(anchor="w", pady=(0, 5))
        
        path_frame = tk.Frame(model_path_frame, bg="white")
        path_frame.pack(fill="x")
        
        self.eval_model_path_entry = tk.Entry(path_frame, font=("Microsoft YaHei", 10), relief="solid", borderwidth=1)
        self.eval_model_path_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        model_select_btn = ttk.Button(path_frame, text="浏览",
                                   style='Custom.TButton',
                                   command=lambda: self.select_file_callback(self.eval_model_path_entry))
        model_select_btn.pack(side="right", padx=(10, 0))

        # 第三页：评估数据选择
        eval_page3 = tk.Frame(self.parent_frame, bg="white")
        self.pages.append(eval_page3)  # 先添加到页面列表
        
        content3 = self.create_scrollable_frame(eval_page3)
        
        title3 = tk.Label(content3, text="选择评估数据",
                         font=("Microsoft YaHei", 14, "bold"),
                         bg="white", fg="#2c3e50")
        title3.pack(pady=(20, 15))
        
        data_frame = tk.Frame(content3, bg="white")
        data_frame.pack(fill="x")
        
        tk.Label(data_frame, text="验证集文件路径：",
                font=("Microsoft YaHei", 10),
                bg="white", fg="#34495e").pack(anchor="w", pady=(0, 5))
        
        data_path_frame = tk.Frame(data_frame, bg="white")
        data_path_frame.pack(fill="x")
        
        self.eval_data_path_entry = tk.Entry(data_path_frame, font=("Microsoft YaHei", 10), relief="solid", borderwidth=1)
        self.eval_data_path_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        data_select_btn = ttk.Button(data_path_frame, text="浏览",
                                  style='Custom.TButton',
                                  command=lambda: self.select_file_callback(self.eval_data_path_entry))
        data_select_btn.pack(side="right", padx=(10, 0))

        # 第四页：开始评估与结果
        eval_page4 = tk.Frame(self.parent_frame, bg="white")
        self.pages.append(eval_page4)  # 先添加到页面列表
        
        content4 = self.create_scrollable_frame(eval_page4)
        
        title4 = tk.Label(content4, text="评估结果",
                         font=("Microsoft YaHei", 14, "bold"),
                         bg="white", fg="#2c3e50")
        title4.pack(pady=(20, 25))
        
        btn_frame = tk.Frame(content4, bg="white")
        btn_frame.pack(fill="x")
        
        start_eval_btn = ttk.Button(btn_frame, text="开始评估",
                                  style='Custom.TButton',
                                  command=self._start_evaluation)
        start_eval_btn.pack()
        
        result_frame = tk.Frame(content4, bg="white")
        result_frame.pack(fill="both", expand=True, pady=(20, 0))
        
        result_title = tk.Label(result_frame, text="评估报告：",
                              font=("Microsoft YaHei", 11, "bold"),
                              bg="white", fg="#2c3e50")
        result_title.pack(anchor="w", pady=(0, 10))
        
        self.eval_result_label = tk.Label(result_frame, text="等待评估...",
                                       font=("Microsoft YaHei", 10),
                                       bg="white", fg="#7f8c8d",
                                       justify="left", wraplength=300)
        self.eval_result_label.pack(fill="x")

    def create_nav_buttons(self):
        """
        创建页面导航按钮和进度指示器。
        """
        nav_frame = tk.Frame(self.parent_frame, bg="white")
        nav_frame.pack(side="bottom", pady=15, padx=30, fill="x")
        
        # 创建进度指示器
        self.progress_dots = []
        dots_frame = tk.Frame(nav_frame, bg="white")
        dots_frame.pack(side="top", pady=(0, 10))
        
        for i in range(4):  # 四个页面的指示器
            dot = tk.Label(dots_frame, text="●", font=("Arial", 12),
                          bg="white", fg="#bdc3c7")
            dot.pack(side="left", padx=5)
            self.progress_dots.append(dot)
        
        # 创建按钮容器
        button_frame = tk.Frame(nav_frame, bg="white")
        button_frame.pack(fill="x")
        
        self.prev_button = ttk.Button(button_frame, text="上一步",
                                   style='Nav.TButton', command=self.prev_page)
        self.prev_button.pack(side="left")

        self.next_button = ttk.Button(button_frame, text="下一步",
                                   style='Nav.TButton', command=self.next_page)
        self.next_button.pack(side="right")

    def show_page(self, page_index):
        """
        根据索引显示指定的分页。
        """
        if not (0 <= page_index < len(self.pages)):
            return
            
        # 解绑所有画布的鼠标滚轮事件
        for canvas_tuple in self.scroll_canvases:
            canvas_tuple[0].unbind_all("<MouseWheel>")
        
        # 隐藏所有页面
        for page in self.pages:
            page.pack_forget()
        
        # 显示当前页面
        self.pages[page_index].pack(expand=True, fill="both")
        self.current_page = page_index
        
        # 绑定当前页面的鼠标滚轮事件并更新滚动区域
        if page_index < len(self.scroll_canvases):
            canvas, update_func = self.scroll_canvases[page_index]
            def on_mousewheel(event):
                canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            canvas.bind_all("<MouseWheel>", on_mousewheel)
            # 等待页面完全显示后更新滚动区域
            self.parent_frame.after(100, lambda: (
                self.parent_frame.update_idletasks(),
                update_func()
            ))

        # 更新按钮状态
        self.prev_button["state"] = "normal" if self.current_page > 0 else "disabled"
        self.next_button["state"] = "normal" if self.current_page < len(self.pages) - 1 else "disabled"
        
        # 更新进度指示器
        for i, dot in enumerate(self.progress_dots):
            if i == page_index:
                dot.configure(fg="#3498db", font=("Arial", 14))  # 当前页面
            elif i < page_index:
                dot.configure(fg="#2ecc71", font=("Arial", 12))  # 已完成页面
            else:
                dot.configure(fg="#bdc3c7", font=("Arial", 12))  # 未完成页面

    def next_page(self):
        """显示下一页。"""
        if self.current_page < len(self.pages) - 1:
            self.show_page(self.current_page + 1)

    def prev_page(self):
        """显示上一页。"""
        if self.current_page > 0:
            self.show_page(self.current_page - 1)

    def _get_evaluation_params(self):
        """
        统一获取UI界面的所有评估参数。
        
        Returns:
            tuple: (模型文件路径, 评估数据路径)
        """
        return (
            self.eval_model_path_entry.get(),
            self.eval_data_path_entry.get()
        )
        
    def _start_evaluation(self):
        """
        UI触发函数，检查参数后调用后端评估管理类的启动方法。
        """
        model_path, data_path = self._get_evaluation_params()
        model_name = self.eval_model_combo.get()

        if not all([model_path, data_path, model_name]):
            self.eval_result_label.config(text="请确保填写了所有必要的参数!")
            return

        self.evaluation_manager.start_evaluation(
            model_path,
            data_path,
            model_name,
            self._update_result_label
        )

    def _update_result_label(self, result_text):
        """
        回调函数，用于接收后端结果并更新UI标签。
        """
        self.eval_result_label.config(text=result_text)