import torch
import torch.nn as nn
from torch.nn import functional as F
from torch.distributions import Categorical

# 基础网络类 BaseNetwork ，它继承自PyTorch的 nn.Module
class BaseNetwork(nn.Module):
    def save(self, path):
        torch.save(self.state_dict(), path)

    def load(self, path):
        self.load_state_dict(torch.load(path))

class QNetwork(BaseNetwork):

    def __init__(self, state_dim, num_actions, dueling_net=False):
        super().__init__()

        if not dueling_net:
            self.head = nn.Sequential(  # 标准Q网络头部
                nn.Linear(state_dim, 512),
                nn.ReLU(inplace=True),
                nn.Linear(512, 256),
                nn.ReLU(inplace=True),
                nn.Linear(256, 128),
                nn.ReLU(inplace=True),
                nn.Linear(128, num_actions)
            )
        else:
            # 对偶网络结构
            self.a_head = nn.Sequential(  # 优势函数头部
                nn.Linear(state_dim, 512),
                nn.<PERSON>L<PERSON>(inplace=True),
                nn.Linear(512, 256),
                nn.<PERSON><PERSON>(inplace=True),
                nn.Linear(256, 128),
                nn.<PERSON><PERSON>(inplace=True),
                nn.Linear(128, num_actions)
            )
            self.v_head = nn.Sequential(  # 状态值函数头部
                nn.Linear(state_dim, 512),
                nn.ReLU(inplace=True),  
                nn.Linear(512, 256),
                nn.ReLU(inplace=True),
                nn.Linear(256, 128),
                nn.ReLU(inplace=True),
                nn.Linear(128, 1)
            )

        self.dueling_net = dueling_net

    def forward(self, states):
        if not self.dueling_net:
            return self.head(states)
        else:
            a = self.a_head(states) # 计算优势函数
            v = self.v_head(states) # 计算状态值函数
            return v + a - a.mean(1, keepdim=True)  # 计算Q值


class TwinnedQNetwork(BaseNetwork):
    def __init__(self, state_dim, num_actions, dueling_net=False):
        super().__init__()
        self.Q1 = QNetwork(state_dim, num_actions, dueling_net)
        self.Q2 = QNetwork(state_dim, num_actions, dueling_net)

    def forward(self, states):
        q1 = self.Q1(states)
        q2 = self.Q2(states)
        return q1, q2


class CateoricalPolicy(BaseNetwork):

    def __init__(self, state_dim, num_actions):
        super().__init__()

        self.head = nn.Sequential(
            nn.Linear(state_dim, 512),
            nn.ReLU(inplace=True),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True), 
            nn.Linear(128, num_actions)
        )

    def act(self, states):
        action_logits = self.head(states)
        greedy_actions = torch.argmax(
            action_logits, dim=1, keepdim=True)
        return greedy_actions

    def sample(self, states):
        # 通过策略网络的头部获取动作的原始输出
        # 使用 softmax 函数将输出转换为概率分布
        # dim=1 表示在动作维度上进行 softmax
        # 对于输入向量 x，softmax(x_i) = exp(x_i) / Σ exp(x_j)
        action_probs = F.softmax(self.head(states), dim=1)
        # - - 使用概率分布创建一个类别分布对象
        # - Categorical 是 PyTorch 中的离散分布类
        # - 用于后续的动作采样
        action_dist = Categorical(action_probs)
        # - 从类别分布中采样动作
        # - sample() 根据概率随机选择动作
        # - view(-1, 1) 将结果重塑为列向量
        # - 根据概率分布进行随机采样
        # - 概率越大的动作被选中的机会越大，但不是必然被选中
        # 平衡了探索和利用：高概率动作更容易被选中，但低概率动作也有机会
        actions = action_dist.sample().view(-1, 1)

        # Avoid numerical instability.
        # - 创建一个掩码 z ，在概率为 0 的位置填充很小的值
        # - 防止计算对数时出现数值不稳定（log(0)是未定义的）
        # - 计算动作概率的对数值
        z = (action_probs == 0.0).float() * 1e-8
        log_action_probs = torch.log(action_probs + z)

        # actions - 从概率分布中采样得到的具体动作索引
        # action_probs - 每个动作被选中的概率
        # log_action_probs - 每个动作概率的对数值
        return actions, action_probs, log_action_probs
