from collections import deque
import numpy as np
import torch


class MultiStepBuff:

    def __init__(self, maxlen=3):
        super(MultiStepBuff, self).__init__()
        self.maxlen = int(maxlen)
        self.reset()

    def append(self, state, action, reward):
        self.states.append(state)
        self.actions.append(action)
        self.rewards.append(reward)

    def get(self, gamma=0.99):
        assert len(self.rewards) > 0
        state = self.states.popleft()   # 移除并返回队列最左端（最早加入）的元素
        action = self.actions.popleft()
        reward = self._nstep_return(gamma)
        return state, action, reward

    def _nstep_return(self, gamma):
        r = np.sum([r * (gamma ** i) for i, r in enumerate(self.rewards)])
        self.rewards.popleft()
        return r

    def reset(self):
        # Buffer to store n-step transitions.
        self.states = deque(maxlen=self.maxlen) # deque （双端队列，double-ended queue），可以从两端进行添加和删除操作
        self.actions = deque(maxlen=self.maxlen)
        self.rewards = deque(maxlen=self.maxlen)

    def is_empty(self):
        return len(self.rewards) == 0

    def is_full(self):
        return len(self.rewards) == self.maxlen

    def __len__(self):
        return len(self.rewards)


class LazyMemory(dict):

    def __init__(self, capacity, state_shape, device):
        super(LazyMemory, self).__init__()  # 继承自python内置的dict（字典）
        self.capacity = int(capacity)
        self.state_shape = state_shape
        self.device = device
        self.reset()    # 调用reset方法，初始化内存

    def reset(self):
        self['state'] = []
        self['next_state'] = []

        self['action'] = np.empty((self.capacity, 1), dtype=np.int64)
        self['reward'] = np.empty((self.capacity, 1), dtype=np.float32)
        self['done'] = np.empty((self.capacity, 1), dtype=np.float32)

        self._n = 0  # 记录已存储的经验数量
        self._p = 0  # 记录下一个存储位置的索引
    
    # 外部调用添加元素的操作
    def append(self, state, action, reward, next_state, done,
               episode_done=None):  # 公共API接口，不影响内部数据结构
        self._append(state, action, reward, next_state, done)
    # 下划线前缀表示内部方法，可以在不影响外部调用的情况下修改内部实现
    def _append(self, state, action, reward, next_state, done):
        self['state'].append(state)
        self['next_state'].append(next_state)
        self['action'][self._p] = action
        self['reward'][self._p] = reward
        self['done'][self._p] = done

        self._n = min(self._n + 1, self.capacity)
        self._p = (self._p + 1) % self.capacity

        self.truncate()

    def truncate(self):
        while len(self['state']) > self.capacity:
            del self['state'][0]
            del self['next_state'][0]
    
    # 外部调用采样元素的操作
    def sample(self, batch_size):
        indices = np.random.randint(low=0, high=len(self), size=batch_size)
        return self._sample(indices, batch_size)

    def _sample(self, indices, batch_size):
        bias = -self._p if self._n == self.capacity else 0

        # *在这里是Python的解包操作符，创建np数据，第一维为批次大小，后续为状态的形状
        states = np.empty(
            (batch_size, *self.state_shape), dtype=np.float32)    
        next_states = np.empty(
            (batch_size, *self.state_shape), dtype=np.float32)

        for i, index in enumerate(indices): # enumerate()函数用于同时获取索引和元素
            _index = np.mod(index+bias, self.capacity)  # 缓存区循环保存，计算索引对应的实际的数据的位置
            states[i, ...] = self['state'][_index]  # 三个点可以自动扩展维度，适应状态的维度
            next_states[i, ...] = self['next_state'][_index]

        states = torch.FloatTensor(states).to(self.device) # 转化为张量
        next_states = torch.FloatTensor(next_states).to(self.device)
        actions = torch.IntTensor(self['action'][indices]).to(self.device)
        rewards = torch.FloatTensor(self['reward'][indices]).to(self.device)
        dones = torch.FloatTensor(self['done'][indices]).to(self.device)

        return states, actions, rewards, next_states, dones

    def __len__(self):  # 魔术方法，用于获取对象的长度
        return self._n


class LazyMultiStepMemory(LazyMemory):

    def __init__(self, capacity, state_shape, device, gamma=0.99,
                 multi_step=3):
        super(LazyMultiStepMemory, self).__init__(  # super下的参数是一种显式写法，可有可无
            capacity, state_shape, device)

        self.gamma = gamma
        self.multi_step = int(multi_step)
        if self.multi_step != 1:
            self.buff = MultiStepBuff(maxlen=self.multi_step)

    def append(self, state, action, reward, next_state, done):
        if self.multi_step != 1:
            self.buff.append(state, action, reward)

            if self.buff.is_full():
                state, action, reward = self.buff.get(self.gamma)
                self._append(state, action, reward, next_state, done)

            if done:
                while not self.buff.is_empty():
                    state, action, reward = self.buff.get(self.gamma)
                    self._append(state, action, reward, next_state, done)
        else:
            self._append(state, action, reward, next_state, done)
