# backend/eval.py
import threading
import os
import sqlite3
from tkinter import messagebox
from backend.core.RLWIDM import model_evaluate_RLWIDM

from backend.core.RLRPPDM import model_evaluate_RLRPPDM

class EvaluationManager:
    """
    评估管理类，封装所有评估相关的后端逻辑和线程管理。
    """
    def __init__(self):
        self._evaluation_thread = None

    def _validate_database_file(self, data_path):
        """
        验证数据库文件是否合法

        Args:
            data_path (str): 数据库文件路径

        Returns:
            tuple: (is_valid, error_message)
        """
        # 检查文件是否存在
        if not os.path.exists(data_path):
            return False, f"数据库文件不存在：{data_path}"

        # 检查文件是否为空
        if os.path.getsize(data_path) == 0:
            return False, f"数据库文件为空：{data_path}"

        # 检查文件扩展名
        if not data_path.lower().endswith('.db'):
            return False, f"文件不是数据库文件（.db）：{data_path}"

        # 尝试连接数据库
        try:
            conn = sqlite3.connect(data_path)
            cursor = conn.cursor()

            # 根据文件名确定应该存在的表名
            db_filename = os.path.basename(data_path)
            expected_table = None
            if 'test_data.db' in db_filename:
                expected_table = 'test_table'
            elif 'train_data.db' in db_filename:
                expected_table = 'train_table'
            elif 'all_data.db' in db_filename:
                expected_table = 'all_table'

            # 检查表是否存在
            if expected_table:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (expected_table,))
                if not cursor.fetchone():
                    conn.close()
                    return False, f"数据库中缺少预期的表：{expected_table}"

                # 检查表是否有数据
                cursor.execute(f"SELECT COUNT(*) FROM {expected_table}")
                row_count = cursor.fetchone()[0]
                if row_count == 0:
                    conn.close()
                    return False, f"数据库表 {expected_table} 中没有数据"

            conn.close()
            return True, "数据库文件验证通过"

        except sqlite3.Error as e:
            return False, f"数据库连接或查询错误：{str(e)}"
        except Exception as e:
            return False, f"验证数据库时发生未知错误：{str(e)}"
    
    def start_evaluation(self, model_path, data_path, model_name, result_callback):
        """
        启动模型评估过程。

        Args:
            model_path (str): 模型文件路径。
            data_path (str): 评估数据文件路径。
            model_name (str): 模型名称。
            result_callback (callable): 评估完成后用于更新UI的回调函数。
        """
        if self._evaluation_thread and self._evaluation_thread.is_alive():
            print("评估已在运行中。")
            return

        # 通知UI“正在评估中...”
        result_callback("评估中...")

        # 在新线程中运行评估核心逻辑
        self._evaluation_thread = threading.Thread(
            target=self._run_evaluation_core,
            args=(model_path, data_path, model_name, result_callback)
        )
        self._evaluation_thread.daemon = True
        self._evaluation_thread.start()
        print("评估已启动。")
        
    def _run_evaluation_core(self, model_path, data_path, model_name, result_callback):
        """
        评估核心逻辑，调用真实的模型评估函数。
        """
        print(f"后端：开始评估模型: {model_name}，模型文件: {model_path}，使用数据文件: {data_path}")

        # 使用字典映射模型名称和评估函数
        model_evaluators = {
            "基于强化学习的波形决策模型": model_evaluate_RLWIDM,
            "基于强化学习的路由参数决策模型": model_evaluate_RLRPPDM,
        }

        if model_name not in model_evaluators:
            error_msg = f"不支持的模型类型：{model_name}"
            print(f"后端：{error_msg}")
            result_callback(error_msg)
            return

        try:
            # 验证数据库文件
            is_valid, error_message = self._validate_database_file(data_path)
            if not is_valid:
                # 显示弹窗提示用户
                messagebox.showerror("数据库文件错误", f"没有合法的数据库文件！\n\n错误详情：\n{error_message}")
                result_callback(f"数据库验证失败：{error_message}")
                return

            # 获取对应的评估函数并执行
            evaluator = model_evaluators[model_name]
            print(f"调用评估函数: {evaluator.__name__}")

            # 根据数据库文件名确定表名
            db_filename = os.path.basename(data_path)
            if 'test_data.db' in db_filename:
                table_name = 'test_table'
            elif 'train_data.db' in db_filename:
                table_name = 'train_table'
            elif 'all_data.db' in db_filename:
                table_name = 'all_table'
            else:
                # 默认使用test_table
                table_name = 'test_table'

            print(f"数据库文件: {db_filename}, 使用表名: {table_name}")

            # 调用真实的模型评估函数
            accuracy = evaluator(model_path, data_path, table_name)

            if accuracy is not None:
                # 将准确率转换为百分比格式
                accuracy_percent = round(accuracy * 100, 2)
                result = f"正确率: {accuracy_percent}%"
                print(f"后端：评估完成！准确率: {accuracy_percent}%")
            else:
                result = "评估失败：未能获取有效的准确率结果"
                print("后端：评估失败，未能获取有效结果")

            # 调用前端的回调函数来更新UI结果
            result_callback(result)

        except Exception as e:
            error_msg = f"评估出错: {str(e)}"
            print(f"后端：{error_msg}")
            result_callback(error_msg)
