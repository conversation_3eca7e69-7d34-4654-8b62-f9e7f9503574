# backend/eval.py
import threading
from backend.core.RLWIDM import model_evaluate_RLWIDM

class EvaluationManager:
    """
    评估管理类，封装所有评估相关的后端逻辑和线程管理。
    """
    def __init__(self):
        self._evaluation_thread = None
    
    def start_evaluation(self, model_path, data_path, model_name, result_callback):
        """
        启动模型评估过程。

        Args:
            model_path (str): 模型文件路径。
            data_path (str): 评估数据文件路径。
            model_name (str): 模型名称。
            result_callback (callable): 评估完成后用于更新UI的回调函数。
        """
        if self._evaluation_thread and self._evaluation_thread.is_alive():
            print("评估已在运行中。")
            return

        # 通知UI“正在评估中...”
        result_callback("评估中...")

        # 在新线程中运行评估核心逻辑
        self._evaluation_thread = threading.Thread(
            target=self._run_evaluation_core,
            args=(model_path, data_path, model_name, result_callback)
        )
        self._evaluation_thread.daemon = True
        self._evaluation_thread.start()
        print("评估已启动。")
        
    def _run_evaluation_core(self, model_path, data_path, model_name, result_callback):
        """
        评估核心逻辑，调用真实的模型评估函数。
        """
        print(f"后端：开始评估模型: {model_name}，模型文件: {model_path}，使用数据文件: {data_path}")

        # 使用字典映射模型名称和评估函数
        model_evaluators = {
            "基于强化学习的波形决策模型": model_evaluate_RLWIDM,
        }

        if model_name not in model_evaluators:
            error_msg = f"不支持的模型类型：{model_name}"
            print(f"后端：{error_msg}")
            result_callback(error_msg)
            return

        try:
            # 获取对应的评估函数并执行
            evaluator = model_evaluators[model_name]
            print(f"调用评估函数: {evaluator.__name__}")

            # 根据数据库文件名确定表名
            import os
            db_filename = os.path.basename(data_path)
            if 'test_data.db' in db_filename:
                table_name = 'test_table'
            elif 'train_data.db' in db_filename:
                table_name = 'train_table'
            elif 'all_data.db' in db_filename:
                table_name = 'all_table'
            else:
                # 默认使用test_table
                table_name = 'test_table'

            print(f"数据库文件: {db_filename}, 使用表名: {table_name}")

            # 调用真实的模型评估函数
            accuracy = evaluator(model_path, data_path, table_name)

            if accuracy is not None:
                # 将准确率转换为百分比格式
                accuracy_percent = round(accuracy * 100, 2)
                result = f"正确率: {accuracy_percent}%"
                print(f"后端：评估完成！准确率: {accuracy_percent}%")
            else:
                result = "评估失败：未能获取有效的准确率结果"
                print("后端：评估失败，未能获取有效结果")

            # 调用前端的回调函数来更新UI结果
            result_callback(result)

        except Exception as e:
            error_msg = f"评估出错: {str(e)}"
            print(f"后端：{error_msg}")
            result_callback(error_msg)