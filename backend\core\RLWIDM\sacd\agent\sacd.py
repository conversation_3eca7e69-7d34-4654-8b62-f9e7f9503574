import os
import numpy as np
import torch
from torch.optim import Adam, SGD    # Adam 是一个常用的优化器，它的主要作用是在深度学习模型训练过程中更新网络参数。
import torch.optim as optim

from .base import BaseAgent
from sacd.model import TwinnedQNetwork, CateoricalPolicy


class SacdAgent(BaseAgent):

    def __init__(self, env, test_env, log_dir, num_steps=100000, batch_size=64,
                 lr=0.0003, memory_size=1000000, gamma=0.99, multi_step=1,
                 target_entropy_ratio=0.98, start_steps=20000,
                 update_interval=4, target_update_interval=8000,
                 dueling_net=False, num_eval_steps=125000,
                 max_episode_steps=27000, log_interval=10, eval_interval=1000,
                 cuda=True, seed=0, optimizer='Adam'):
        super().__init__(
            env, test_env, log_dir, num_steps, batch_size, memory_size, gamma,
            multi_step, target_entropy_ratio, start_steps, update_interval,
            target_update_interval, num_eval_steps, max_episode_steps,
            log_interval, eval_interval, cuda, seed)
            
        # 添加进度回调
        self._progress_callback = None

        # Define networks.
        self.policy = CateoricalPolicy(
            self.env.observation_shape[0], self.env.action_space['n']).to(self.device)
        self.online_critic = TwinnedQNetwork(
            self.env.observation_shape[0], self.env.action_space['n'], dueling_net=dueling_net).to(device=self.device)
        self.target_critic = TwinnedQNetwork(
            self.env.observation_shape[0], self.env.action_space['n'], dueling_net=dueling_net).to(device=self.device).eval()

        # Copy parameters of the learning network to the target network.
        self.target_critic.load_state_dict(self.online_critic.state_dict())

        # Disable gradient calculations of the target network.
        self.disable_gradients(self.target_critic)

        # 获取选择的优化器类
        if optimizer.lower() == 'adam':
            optimizer_class = Adam
        elif optimizer.lower() == 'sgdm':
            optimizer_class = lambda params, lr: SGD(params, lr=lr, momentum=0.9)
        elif optimizer.lower() == 'nadam':
            optimizer_class = optim.NAdam
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer}。支持的类型包括: adam, sgdm, nadam")
        print(f"优化器类型: {optimizer}")
        # 为不同的网络部分创建优化器实例
        self.policy_optim = optimizer_class(self.policy.parameters(), lr=lr)
        self.q1_optim = optimizer_class(self.online_critic.Q1.parameters(), lr=lr)
        self.q2_optim = optimizer_class(self.online_critic.Q2.parameters(), lr=lr)

        """
        目标熵值的作用：
        - 用于控制策略的随机性
        - 较大的熵值鼓励探索
        - 较小的熵值倾向于确定性策略
        - 通过 target_entropy_ratio 可以灵活调整探索-利用的平衡
        """
        self.target_entropy = \
            -np.log(1.0 / self.env.action_space['n']) * target_entropy_ratio

        # We optimize log(alpha), instead of alpha.
        # SAC 算法中温度参数 α 的自动调节机制
        # - 较大的α值：
        # - 增加策略的随机性
        # - 鼓励更多的探索
        # - 使策略更倾向于尝试不同的动作

        # - 较小的α值：
        # - 减少策略的随机性
        # - 倾向于选择最优动作
        # - 使策略更确定性
        self.log_alpha = torch.zeros(1, requires_grad=True, device=self.device)  # 初始化为1，允许梯度更新
        self.alpha = self.log_alpha.exp()  # 通过指数函数确保 alpha 始终为正
        # 使用相同类型的优化器
        self.alpha_optim = optimizer_class([self.log_alpha], lr=lr)  # 创建优化器来更新 log_alpha

    def explore(self, state):
        # Act with randomness.
        state = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        with torch.no_grad():
            action, _, _ = self.policy.sample(state)
        return action.item()

    def exploit(self, state):
        # Act without randomness.
        state = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        with torch.no_grad():
            action = self.policy.act(state)
        return action.item()

    def set_progress_callback(self, callback):
        """
        设置进度回调函数
        
        Args:
            callback (callable): 回调函数，接受三个参数：当前步数，总步数，额外信息
        """
        self._progress_callback = callback

    def update_target(self, tau = 0.005):
        # tau 是软更新系数，通常取 0.005 或 0.01
        for target_param, online_param in zip(self.target_critic.parameters(), self.online_critic.parameters()):
            target_param.data.copy_(tau * online_param.data + (1.0 - tau) * target_param.data)

    def calc_current_q(self, states, actions, rewards, next_states, dones):
        # 使用在线评论家网络计算两个 Q 值
        curr_q1, curr_q2 = self.online_critic(states)
        # 使用 gather 操作从 Q 值中选择实际执行的动作对应的值
        curr_q1 = curr_q1.gather(1, actions.long())  # 从第一个 Q 网络获取选定动作的 Q 值
        curr_q2 = curr_q2.gather(1, actions.long())  # 从第二个 Q 网络获取选定动作的 Q 值
        return curr_q1, curr_q2

    def calc_target_q(self, states, actions, rewards, next_states, dones):
        with torch.no_grad():  # 不需要计算梯度，因为这是目标值
            # 对下一个状态采样动作及其概率
            _, action_probs, log_action_probs = self.policy.sample(next_states)
            # 使用目标网络计算下一状态的 Q 值
            next_q1, next_q2 = self.target_critic(next_states)
            # 计算软 Q 值期望：Q值减去熵的加权和
            next_q = (action_probs * (
                torch.min(next_q1, next_q2) - self.alpha * log_action_probs
                )).sum(dim=1, keepdim=True)
    
        assert rewards.shape == next_q.shape
        # 贝尔曼方程：当前奖励 + 折扣因子 * 非终止状态的下一步预期回报
        return rewards + (1.0 - dones) * self.gamma_n * next_q

    def calc_critic_loss(self, batch):
        curr_q1, curr_q2 = self.calc_current_q(*batch)
        target_q = self.calc_target_q(*batch)

        # We log means of Q to monitor training.
        mean_q1 = curr_q1.detach().mean().item()
        mean_q2 = curr_q2.detach().mean().item()

        # Critic loss is mean squared TD errors
        q1_loss = torch.mean((curr_q1 - target_q).pow(2))
        q2_loss = torch.mean((curr_q2 - target_q).pow(2))

        return q1_loss, q2_loss, mean_q1, mean_q2

    def calc_policy_loss(self, batch):
        states, actions, rewards, next_states, dones = batch

        # (Log of) probabilities to calculate expectations of Q and entropies.
        _, action_probs, log_action_probs = self.policy.sample(states)

        with torch.no_grad():
            # Q for every actions to calculate expectations of Q.
            q1, q2 = self.online_critic(states)

        # Expectations of entropies.
        # 计算策略的熵（entropy）
        entropies = -torch.sum(
            action_probs * log_action_probs, dim=1, keepdim=True)

        # Expectations of Q.
        q = torch.sum(torch.min(q1, q2) * action_probs, dim=1, keepdim=True)

        # Policy objective is maximization of (Q + alpha * entropy)
        policy_loss = ((- q - self.alpha * entropies)).mean()
        
        return policy_loss, entropies.detach() # 让熵的梯度不会回传到策略网络，只影响温度参数的优化，是实现 SAC 算法自动温度调节的关键步骤

    # 计算SAC算法中的熵损失（entropy loss），用于自动调节温度参数α。
    def calc_entropy_loss(self, entropies):
        assert not entropies.requires_grad

        # Intuitively, we increse alpha when entropy is less than target
        # entropy, vice versa.
        entropy_loss = -torch.mean(
            self.log_alpha * (self.target_entropy - entropies))
        return entropy_loss

    def save_models(self, save_dir):
        super().save_models(save_dir)
        self.policy.save(os.path.join(save_dir, 'policy.pth'))
        self.online_critic.save(os.path.join(save_dir, 'online_critic.pth'))
        self.target_critic.save(os.path.join(save_dir, 'target_critic.pth'))
