# type: ignore
"""
进度条模块

显示模型训练的进度
"""

# ui/progress_bar.py
import tkinter as tk
from tkinter import ttk

class ProgressBarModule:
    """
    进度条UI逻辑类，负责创建和管理训练进度条。
    它现在只负责UI的显示，不包含任何训练逻辑或线程管理。
    """
    def __init__(self, parent_frame):
        """
        初始化进度条模块。
        
        Args:
            parent_frame (tk.Frame): 承载进度条的父框架。
        """
        self.parent_frame = parent_frame
        self.progressbar = None
        self.progress_percent_label = None
        self.setup_widgets()
    
    def setup_widgets(self):
        """
        创建并布局进度条和相关标签。
        """
        # 创建进度条样式
        style = ttk.Style()
        style.configure("Custom.Horizontal.TProgressbar",
                      thickness=15,
                      troughcolor='#f0f0f0',
                      background='#3498db')

        # 创建上方信息框架
        info_frame = tk.Frame(self.parent_frame, bg="white")
        info_frame.pack(fill="x", padx=20, pady=(15, 10))
        
        progress_label = tk.Label(info_frame, 
                                text="训练进度", 
                                font=("Microsoft YaHei", 12, "bold"),
                                bg="white",
                                fg="#2c3e50")
        progress_label.pack(side="left")

        self.progress_percent_label = tk.Label(info_frame,
                                             text="0%",
                                             font=("Microsoft YaHei", 12),
                                             bg="white",
                                             fg="#7f8c8d")
        self.progress_percent_label.pack(side="right")

        # 创建进度条
        progress_frame = tk.Frame(self.parent_frame, bg="white")
        progress_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        self.progressbar = ttk.Progressbar(progress_frame,
                                         orient="horizontal",
                                         mode="determinate",
                                         style="Custom.Horizontal.TProgressbar")
        self.progressbar.pack(fill="x", expand=True)

        # 创建状态信息标签
        self.status_label = tk.Label(self.parent_frame,
                                   text="等待开始训练...",
                                   font=("Microsoft YaHei", 10),
                                   bg="white",
                                   fg="#95a5a6")
        self.status_label.pack(pady=(0, 15))

    def update_progress(self, value, text):
        """
        接收来自后端的进度更新，并更新UI控件。

        Args:
            value (int): 进度条的值（0-100）。
            text (str): 状态信息文本。
        """
        self.progressbar["value"] = value
        self.progress_percent_label.config(text=f"{value}%")
        
        # 更新状态标签
        self.status_label.config(text=text)
        
        # 根据进度更新颜色
        style = ttk.Style()
        if value == 100:
            style.configure("Custom.Horizontal.TProgressbar",
                          background='#2ecc71')  # 完成时显示绿色
        elif value == 0:
            style.configure("Custom.Horizontal.TProgressbar",
                          background='#3498db')  # 开始时显示蓝色
        
        # 刷新界面
        self.parent_frame.update()
        self.parent_frame.update_idletasks() # 强制更新UI