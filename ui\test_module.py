# type: ignore
"""
测试模块

手动输入模型所需参数，测试模型能否正常工作
"""

import tkinter as tk
from tkinter import ttk, filedialog
from backend.manager import TestManager # 导入后端测试管理类

class TestModule:
    """
    模型测试模块的UI逻辑类
    """
    # 定义不同模型的参数配置
    MODEL_PARAMS = {
        "基于深度神经网络的波形智能决策模型": {
            "体制": {"default": "", "unit": ""},
            "调制方式": {"default": "", "unit": ""},
            "中心频率": {"default": "", "unit": "MHz"},
            "带宽": {"default": "", "unit": "MHz"},
            "电平": {"default": "", "unit": "dBm"},
            "信噪比": {"default": "", "unit": "dB"}
        },
        "基于强化学习的波形决策模型": {
            "体制": {"default": "1", "unit": ""},
            "样式": {"default": "3", "unit": ""},
            "调制方式": {"default": "4", "unit": ""},
            "中心频率": {"default": "50000000", "unit": "Hz"},
            "带宽": {"default": "25000", "unit": "Hz"},
            "功率": {"default": "40", "unit": "dBm"}
        },
        "基于决策树的MAC协议优选模型": {
            "节点数量": {"default": "", "unit": "个"},
            "流量负载": {"default": "", "unit": "Mbps"},
            "干扰强度": {"default": "", "unit": "dB"},
            "时延要求": {"default": "", "unit": "ms"}
        },
        "基于模糊推理神经网络的路由协议优选模型": {
            "网络规模": {"default": "", "unit": "节点"},
            "移动速度": {"default": "", "unit": "m/s"},
            "链路质量": {"default": "", "unit": ""},
            "能量约束": {"default": "", "unit": "J"}
        },
        "基于贝叶斯网络的路由协议参数决策模型": {
            "节点密度": {"default": "", "unit": "个/km²"},
            "链路可靠性": {"default": "", "unit": "%"},
            "端到端延迟": {"default": "", "unit": "ms"},
            "吞吐量需求": {"default": "", "unit": "Mbps"}
        },
        "基于强化学习的路由参数决策模型": {
            "网络规模": {"default": "10", "unit": "个"},
            "邻居个数": {"default": "4", "unit": "个"},
            "经度": {"default": "82.13", "unit": "度"},
            "纬度": {"default": "17.95", "unit": "度"},
            "平均连接度": {"default": "2.78", "unit": ""},
            "邻居变化率": {"default": "0.07", "unit": ""},
            "数据速率": {"default": "7.46", "unit": ""},
            "路由协议": {"default": "OLSR", "unit": ""}
        }
    }
    
    def __init__(self, parent_frame, select_file_callback):
        """
        初始化测试模块
        
        Args:
            parent_frame (tk.Frame): 承载该模块页面的父框架
            select_file_callback (function): 从主应用传入的文件选择回调函数
        """
        self.parent_frame = parent_frame
        self.select_file_callback = select_file_callback
        self.test_manager = TestManager() # 实例化后端管理类
        self.current_page = 0
        self.pages = []
        self.scroll_canvases = []  # 存储所有页面的画布和更新函数
        self.progress_dots = []  # 存储页面指示器圆点
        
        # 存储UI控件的引用
        self.test_model_combo = None
        self.test_model_path_entry = None
        self.test_result_label = None
        self.param_entries = {}

        self.create_pages()
        self.create_nav_buttons()
        self.show_page(self.current_page)
    
    def create_scrollable_frame(self, parent):
        """
        创建一个带滚动条的框架
        
        Args:
            parent: 父容器
            
        Returns:
            content_frame: 内容框架
        """
        # 创建画布和滚动条
        canvas = tk.Canvas(parent, bg="white", highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        
        # 创建内容框架
        content_frame = tk.Frame(canvas, bg="white")
        
        # 配置画布的滚动区域
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 放置画布和滚动条
        scrollbar.pack(side="right", fill="y", padx=(0, 2))
        canvas.pack(side="left", fill="both", expand=True, padx=(10, 2))
        
        # 创建画布窗口
        window = canvas.create_window((0, 0), window=content_frame, anchor="nw")
        
        # 配置画布滚动范围
        def configure_scroll_region(event=None):
            canvas.configure(scrollregion=canvas.bbox("all"))
            # 设置内容框架的宽度，确保至少有400像素宽
            min_width = max(400, canvas.winfo_width())
            canvas.itemconfig(window, width=min_width)
        
        content_frame.bind("<Configure>", configure_scroll_region)
        canvas.bind("<Configure>", configure_scroll_region)
        
        # 绑定鼠标滚轮事件
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        canvas.bind_all("<MouseWheel>", on_mousewheel)
        self.scroll_canvases.append((canvas, configure_scroll_region))
        
        return content_frame

    def create_pages(self):
        """
        创建测试模块的四个分页
        """
        # 设置按钮样式
        style = ttk.Style()
        style.configure('Custom.TButton', 
                      font=('Microsoft YaHei', 10),
                      padding=5)
                      
        style.configure('Nav.TButton', 
                      font=('Microsoft YaHei', 10),
                      padding=5,
                      width=10)
        
        models = [
            "基于深度神经网络的波形智能决策模型",
            "基于强化学习的波形决策模型", 
            "基于决策树的MAC协议优选模型",
            "基于模糊推理神经网络的路由协议优选模型",
            "基于贝叶斯网络的路由协议参数决策模型",
            "基于强化学习的路由参数决策模型"]

        # 第一页：模型选择
        test_page1 = tk.Frame(self.parent_frame, bg="white")
        self.pages.append(test_page1)
        
        content1 = self.create_scrollable_frame(test_page1)
        
        title1 = tk.Label(content1, text="选择测试模型",
                         font=("Microsoft YaHei", 14, "bold"),
                         bg="white", fg="#2c3e50")
        title1.pack(pady=(20, 15))
        
        model_frame = tk.Frame(content1, bg="white")
        model_frame.pack(fill="x")
        
        tk.Label(model_frame, text="模型类型：",
                font=("Microsoft YaHei", 10),
                bg="white", fg="#34495e").pack(anchor="w", pady=(0, 5))
                
        self.test_model_combo = ttk.Combobox(model_frame, values=models,
                                            font=("Microsoft YaHei", 10),
                                            state="readonly")
        self.test_model_combo.set(models[0])
        self.test_model_combo.pack(fill="x", expand=True)

        # 第二页：模型文件选择
        test_page2 = tk.Frame(self.parent_frame, bg="white")
        self.pages.append(test_page2)
        
        content2 = self.create_scrollable_frame(test_page2)
        
        title2 = tk.Label(content2, text="选择模型文件",
                         font=("Microsoft YaHei", 14, "bold"),
                         bg="white", fg="#2c3e50")
        title2.pack(pady=(20, 15))
        
        model_path_frame = tk.Frame(content2, bg="white")
        model_path_frame.pack(fill="x")
        
        tk.Label(model_path_frame, text="模型文件路径：",
                font=("Microsoft YaHei", 10),
                bg="white", fg="#34495e").pack(anchor="w", pady=(0, 5))
        
        path_frame = tk.Frame(model_path_frame, bg="white")
        path_frame.pack(fill="x")
        
        self.test_model_path_entry = tk.Entry(path_frame, font=("Microsoft YaHei", 10), relief="solid", borderwidth=1)
        self.test_model_path_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        test_model_select_btn = ttk.Button(path_frame, text="浏览",
                                       style='Custom.TButton',
                                       command=lambda: self.select_file_callback(self.test_model_path_entry))
        test_model_select_btn.pack(side="right", padx=(10, 0))

        # 第三页：模型输入参数设置
        test_page3 = tk.Frame(self.parent_frame, bg="white")
        self.pages.append(test_page3)
        
        content3 = self.create_scrollable_frame(test_page3)
        
        title3 = tk.Label(content3, text="参数配置",
                         font=("Microsoft YaHei", 14, "bold"),
                         bg="white", fg="#2c3e50")
        title3.pack(pady=(20, 15))
        
        # 创建参数框架
        self.param_frame = tk.Frame(content3, bg="white")
        self.param_frame.pack(fill="x", padx=20)
        
        # 初始化参数输入框字典
        self.param_entries = {}
            
        # 初始化参数输入框字典
        self.param_entries = {}
        
        # 添加模型选择事件处理
        self.test_model_combo.bind('<<ComboboxSelected>>', self._on_model_selected)
        
        # 初始显示第一个模型的参数
        self._update_param_inputs(self.test_model_combo.get())

        # 第四页：开始测试与结果
        test_page4 = tk.Frame(self.parent_frame, bg="white")
        self.pages.append(test_page4)
        
        content4 = self.create_scrollable_frame(test_page4)
        
        title4 = tk.Label(content4, text="测试结果",
                         font=("Microsoft YaHei", 14, "bold"),
                         bg="white", fg="#2c3e50")
        title4.pack(pady=(20, 25))
        
        btn_frame = tk.Frame(content4, bg="white")
        btn_frame.pack(fill="x")
        
        test_start_btn = ttk.Button(btn_frame, text="开始测试",
                                  style='Custom.TButton',
                                  command=self._start_testing)
        test_start_btn.pack()
        
        result_frame = tk.Frame(content4, bg="white")
        result_frame.pack(fill="both", expand=True, padx=30, pady=(20, 0))
        
        result_title = tk.Label(result_frame, text="预测结果：",
                              font=("Microsoft YaHei", 11, "bold"),
                              bg="white", fg="#2c3e50")
        result_title.pack(anchor="w", pady=(0, 10))
        
        self.test_result_label = tk.Label(result_frame, text="等待测试...",
                                        font=("Microsoft YaHei", 10),
                                        bg="white", fg="#7f8c8d",
                                        justify="left", wraplength=250)
        self.test_result_label.pack(fill="x")

    def create_nav_buttons(self):
        """
        创建页面导航按钮和进度指示器
        """
        nav_frame = tk.Frame(self.parent_frame, bg="white")
        nav_frame.pack(side="bottom", pady=15, padx=30, fill="x")
        
        # 创建进度指示器
        self.progress_dots = []
        dots_frame = tk.Frame(nav_frame, bg="white")
        dots_frame.pack(side="top", pady=(0, 10))
        
        for i in range(4):  # 四个页面的指示器
            dot = tk.Label(dots_frame, text="●", font=("Arial", 12),
                          bg="white", fg="#bdc3c7")
            dot.pack(side="left", padx=5)
            self.progress_dots.append(dot)
        
        # 创建按钮容器
        button_frame = tk.Frame(nav_frame, bg="white")
        button_frame.pack(fill="x")
        
        self.prev_button = ttk.Button(button_frame, text="上一步",
                                   style='Nav.TButton', command=self.prev_page)
        self.prev_button.pack(side="left")

        self.next_button = ttk.Button(button_frame, text="下一步",
                                   style='Nav.TButton', command=self.next_page)
        self.next_button.pack(side="right")

    def show_page(self, page_index):
        """
        根据索引显示指定的分页
        """
        if not (0 <= page_index < len(self.pages)):
            return
            
        # 解绑所有画布的鼠标滚轮事件
        for canvas_tuple in self.scroll_canvases:
            canvas_tuple[0].unbind_all("<MouseWheel>")
        
        # 隐藏所有页面
        for page in self.pages:
            page.pack_forget()
        
        # 显示当前页面
        self.pages[page_index].pack(expand=True, fill="both")
        self.current_page = page_index
        
        # 绑定当前页面的鼠标滚轮事件并更新滚动区域
        if page_index < len(self.scroll_canvases):
            canvas, update_func = self.scroll_canvases[page_index]
            def on_mousewheel(event):
                canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            canvas.bind_all("<MouseWheel>", on_mousewheel)
            # 等待页面完全显示后更新滚动区域
            self.parent_frame.after(100, lambda: (
                self.parent_frame.update_idletasks(),
                update_func()
            ))

        # 更新按钮状态
        self.prev_button["state"] = "normal" if self.current_page > 0 else "disabled"
        self.next_button["state"] = "normal" if self.current_page < len(self.pages) - 1 else "disabled"
        
        # 更新进度指示器
        for i, dot in enumerate(self.progress_dots):
            if i == page_index:
                dot.configure(fg="#3498db", font=("Arial", 14))  # 当前页面
            elif i < page_index:
                dot.configure(fg="#2ecc71", font=("Arial", 12))  # 已完成页面
            else:
                dot.configure(fg="#bdc3c7", font=("Arial", 12))  # 未完成页面

    def next_page(self):
        """
        显示下一页
        """
        if self.current_page < len(self.pages) - 1:
            self.show_page(self.current_page + 1)

    def prev_page(self):
        """
        显示上一页
        """
        if self.current_page > 0:
            self.show_page(self.current_page - 1)

    def _get_testing_params(self):
        """
        统一获取UI界面的所有测试参数
        
        Returns:
            dict: 包含所有参数的字典，包括模型信息和具体参数
        """
        # 获取当前选中的模型名称
        selected_model = self.test_model_combo.get()
        model_path = self.test_model_path_entry.get()
        
        # 获取当前模型对应的参数配置
        model_params_config = self.MODEL_PARAMS.get(selected_model, {})
        
        # 构建参数字典
        testing_params = {
            # 基本信息
            "model_name": selected_model,
            "model_path": model_path,
            
            # 模型参数
            "parameters": {}
        }
        
        # 获取参数值并进行类型转换
        for param_name, entry in self.param_entries.items():
            param_value = entry.get().strip()
            param_config = model_params_config.get(param_name, {})
            
            # 尝试进行类型转换
            try:
                # 根据参数单位判断类型
                unit = param_config.get("unit", "").lower()
                if unit in ["mhz", "dbm", "db", "ms", "m/s", "j", "mbps"]:
                    # 数值类型
                    param_value = float(param_value) if param_value else 0.0
                elif unit in ["个", "节点", "个/km²"]:
                    # 整数类型
                    param_value = int(param_value) if param_value else 0
                elif unit == "%":
                    # 百分比，转换为0-1的浮点数
                    param_value = float(param_value) / 100 if param_value else 0.0
                    
            except ValueError:
                # 如果转换失败，保持原始字符串
                pass
                
            testing_params["parameters"][param_name] = param_value
            
        return testing_params
        
    def _start_testing(self):
        """
        UI触发函数，检查参数后调用后端测试管理类的启动方法
        """
        # 获取所有参数
        testing_params = self._get_testing_params()
        
        # 参数验证
        if not testing_params["model_path"]:
            self.test_result_label.config(text="请选择模型文件!")
            return
            
        if not testing_params["parameters"]:
            self.test_result_label.config(text="请填写测试参数!")
            return
            
        # 检查必填参数
        model_params_config = self.MODEL_PARAMS.get(testing_params["model_name"], {})
        missing_params = []
        for param_name in model_params_config:
            if not testing_params["parameters"].get(param_name):
                missing_params.append(param_name)
                
        if missing_params:
            self.test_result_label.config(text=f"请填写以下参数：\n{', '.join(missing_params)}")
            return
            
        # 启动测试
        self.test_manager.start_testing(
            testing_params,
            self._update_result_label
        )

    def _update_result_label(self, result_text):
        """
        回调函数，用于接收后端结果并更新UI标签
        """
        self.test_result_label.config(text=result_text)
        
    def _on_model_selected(self, event):
        """
        模型选择改变时的事件处理函数
        """
        selected_model = self.test_model_combo.get()
        self._update_param_inputs(selected_model)
        
    def _create_param_row(self, parent, label_text, unit=""):
        """
        创建参数输入行
        """
        row_frame = tk.Frame(parent, bg="white")
        row_frame.pack(fill="x", pady=5)
        
        # 创建包含单位的标签文本
        full_label = f"{label_text}"
        if unit:
            full_label = f"{label_text}({unit})"
        
        label = tk.Label(row_frame, text=full_label,
                       font=("Microsoft YaHei", 10),
                       width=15, bg="white", fg="#34495e",
                       anchor="e")
        label.pack(side="left", padx=(0, 10))
        
        entry = tk.Entry(row_frame, font=("Microsoft YaHei", 10), relief="solid", borderwidth=1)
        entry.pack(side="left", fill="x", expand=True)
        
        return row_frame, entry

    def _update_param_inputs(self, model_name):
        """
        根据选择的模型更新参数输入框
        """
        # 清除现有的参数输入框
        for widget in self.param_frame.winfo_children():
            widget.destroy()
        self.param_entries.clear()
        
        # 获取选中模型的参数配置
        model_params = self.MODEL_PARAMS.get(model_name, {})
        
        # 创建新的参数输入框
        for param_name, param_info in model_params.items():
            row_frame, entry = self._create_param_row(
                self.param_frame,
                f"{param_name}：",
                param_info["unit"]
            )
            # 如果有默认值，设置默认值
            if param_info["default"]:
                entry.insert(0, param_info["default"])
            
            # 保存输入框引用
            self.param_entries[param_name] = entry